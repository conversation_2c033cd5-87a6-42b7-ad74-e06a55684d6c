from pathlib import Path

from src.services.file_service import FileService
from src.utils.logger import LoggerFactory


class LocalFileService(FileService):
    """Service to save files in local folder"""
    def __init__(self, folder_path: Path):
        """
        Args:
            folder_path (Path): Folder path to save the files.
        """
        if not folder_path.exists():
            folder_path.mkdir(parents=True)
        self.folder_path = folder_path
        self._logger = LoggerFactory.get_logger(self.__class__.__name__)

    def save_file(self, file_name: str, file_content: bytes) -> None:
        file_path = self.folder_path / file_name
        file_path.write_bytes(file_content)
        self._logger.info(f'File saved: {file_path.relative_to(file_path.parents[2])}')
