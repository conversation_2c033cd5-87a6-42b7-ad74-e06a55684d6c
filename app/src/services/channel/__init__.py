from abc import ABC, abstractmethod

from src.utils.logger import LoggerFactory
from src.services.channel.message import Message


class Channel(ABC):
    def __init__(self, uuid: str, spider_name: str) -> None:
        self._uuid = uuid
        self._spider_name = spider_name
        self.messages_count = 1
        self._last_message_sent = False

        self._logger = LoggerFactory.get_logger(self.__class__.__name__)
        self._logger.info("Channel initialized!")

    def send(self, message: Message, is_last_message: bool = False) -> None:
        """Send a message to the channel.

        Args:
            message (dict): Message to be sent. Must contain a key named "data" with a dict value.
            is_last_message (bool): If true, the message is the last one to be sent.


        Example:
            # >>> channel = Channel(uuid='123', spider_name='test')
            # >>> channel.send({'data': {'key': 'value'}, 'meta': {} })
            # OR
            # >>> channel.send({'data': {'key': 'value'}, 'meta': {}}, is_last_message=True)
            """
        if self._last_message_sent:
            self._logger.warning("Last message already sent, ignoring this and futeures messages.")
            self._logger.debug('Message ignored: %s', message.to_dict())
            return

        message_dict = self._add_metadata_to_message(message.to_dict(), is_last_message)
        self._send(message_dict)
        self.messages_count += 1
        if is_last_message:
            self._last_message_sent = True

    @abstractmethod
    def _send(self, message: dict) -> None:
        raise NotImplementedError

    def _add_metadata_to_message(self, message: dict, is_last_message: bool) -> dict:
        info = {
            'uuid': self._uuid,
            'spider_name': self._spider_name,
            **message,
            'meta': {
                'last': is_last_message,
                'index': self.messages_count
            }
        }
        return info
