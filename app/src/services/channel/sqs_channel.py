import os
import json

import boto3

from src.services.channel import Channel


class SQSChannel(Channel):
    def __init__(self, uuid: str, spider_name: str, resource_location: str) -> None:
        super().__init__(uuid, spider_name)
        self._resource_location = resource_location
        self.queue = None

    def _queue(self):
        # TODO: using this method to initialize the queue, the error if queue not exists
        #  is only received at try to send a message
        self.queue = self.queue or boto3.resource(
            "sqs",
            region_name="us-east-1",
            aws_access_key_id=os.getenv("NEW_AWS_ACCESS_KEY_ID"),
            aws_secret_access_key=os.getenv("NEW_AWS_SECRET_ACCESS_KEY"),
        ).get_queue_by_name(QueueName=self._resource_location)
        return self.queue

    def _send(self, message: dict) -> None:
        self._logger.info("Sending message to %s", self._resource_location)
        string_message = json.dumps(message)
        self._logger.debug("Payload: %s", string_message)
        self._queue().send_message(MessageBody=string_message)
