from pytest import fixture

from src.validation.mandatory_validation import MandatorySpiderFields


class TestMandatoryValidation:
    @fixture
    def payload(self) -> dict:
        return {
            "uuid": "953332eb-6113-4b25-8176-019521d6e697",
            "spider_name": "autos_embarcador_der_sp",
            "delivery": {"type": "AWS_SQS", "queue_name": "myqueuename"},
        }

    def test_validate(self, payload):
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "success"
        assert result["errors"] == []
        assert result["error_type"] is None

    def test_validate_missing_spider_name(self, payload):
        del payload["spider_name"]
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"spider_name": "Field required"}

    def test_validate_invalid_spider_name(self, payload):
        payload["spider_name"] = "invalid_spider"
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"spider_name": 'Value error, "invalid_spider" is not a valid spider name'}

    def test_validate_missing_delivery(self, payload):
        del payload["delivery"]
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"delivery": "Field required"}

    def test_validate_invalid_delivery(self, payload):
        payload["delivery"] = "FTP"
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"delivery": "Input should be a valid dictionary or instance of DeliverySchema"}

    def test_validate_missing_delivery_type(self, payload):
        del payload["delivery"]["type"]
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"delivery -> type": "Field required"}

    def test_validate_invalid_delivery_type(self, payload):
        payload["delivery"]["type"] = "FTP"
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"delivery -> type": "Input should be 'AWS_SQS'"}

    def test_validate_missing_delivery_queue_name(self, payload):
        del payload["delivery"]["queue_name"]
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"delivery -> queue_name": "Field required"}

    def test_validate_invalid_delivery_queue_name(self, payload):
        payload["delivery"]["queue_name"] = ""
        result = MandatorySpiderFields.validate(payload).to_dict()
        assert result["status"] == "error"
        assert result["errors"][0] == {"delivery -> queue_name": "String should have at least 2 characters"}
