from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from src.credentials.cnpj import <PERSON><PERSON><PERSON>, CNPJUtils


class TestCNPJ:
    def test_call_cnpj_utils_clean(self, mocker: MockerFixture):
        spy = mocker.spy(CNPJUtils, 'clean')
        CNPJ('12345678901234')
        spy.assert_called_once_with('12345678901234')

    def test_value_attribute_is_cleaned(self):
        cnpj = CNPJ('12.345.678/0001-34')
        assert cnpj.value == '12345678000134'

    def test_formatted_property(self):
        cnpj = CNPJ('12345678000134')
        assert cnpj.formatted == '12.345.678/0001-34'
