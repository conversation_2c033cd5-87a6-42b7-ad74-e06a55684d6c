from src.credentials.cnpj.utils import CNPJUtils


class CNPJValidator:
    @classmethod
    def validate(cls, value: str) -> bool:
        if not value:
            return False

        cnpj = CNPJUtils.clean(value)

        if not cnpj.isnumeric():
            return False
        if len(cnpj) != 14:
            return False
        if cls._all_equal(cnpj):
            return False

        checkers = cnpj[-2:]
        digit_one = cls._calculate_first_digit(cnpj)
        digit_two = cls._calculate_second_digit(cnpj)
        return bool(checkers == digit_one + digit_two)

    @staticmethod
    def _all_equal(cnpj: str):
        return all([d == cnpj[0] for d in cnpj])

    @classmethod
    def _calculate_first_digit(cls, cnpj: str) -> str:
        one_validation_list = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
        result = 0
        pos = 0
        for number in cnpj:
            try:
                one_validation_list[pos]
            except IndexError:
                break
            result += int(number) * int(one_validation_list[pos])
            pos += 1
        return cls._calculate_digit(result)

    @classmethod
    def _calculate_second_digit(cls, cnpj: str):
        two_validation_list = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
        result = 0
        pos = 0
        for number in cnpj:
            try:
                two_validation_list[pos]
            except IndexError:
                break
            result += int(number) * int(two_validation_list[pos])
            pos += 1

        return cls._calculate_digit(result)

    @staticmethod
    def _calculate_digit(result: int) -> str:
        result = result % 11
        if result < 2:
            digit = 0
        else:
            digit = 11 - result
        return str(digit)
