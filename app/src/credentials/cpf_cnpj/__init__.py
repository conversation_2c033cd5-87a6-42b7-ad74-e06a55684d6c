import re

from src.credentials import Credential
from src.credentials.cnpj import C<PERSON><PERSON>
from src.credentials.cpf import CPF


class CPForCNPJ:
    @staticmethod
    def from_value(value: str) -> Credential:
        """
        Create a CPF or CNPJ instance from a value.

        Args:
            - value (str): CPF or CNPJ value.

        Returns:
            - Credential: CPF or CNPJ instance.

        Raises:
            - ValueError: If value is not a valid CPF or CNPJ.
        """
        value = re.sub(r"\D", "", value)

        if len(value) == 11:
            return CPF(value)
        elif len(value) == 14:
            return CNPJ(value)
        else:
            raise ValueError('CPF/CNPJ inválido')
