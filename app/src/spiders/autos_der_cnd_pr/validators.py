"""Define validators for DER CND PR."""

from src.credentials.cpf_cnpj.model import CPForCNPJModel
from src.validation.base_data_validator import BaseDataValidator, BasePayloadValidator


class DerCndPrDataValidator(BaseDataValidator, CPForCNPJModel):
    """Pydantic model to validate `data` key in input."""


class DerCndPrPayloadValidator(BasePayloadValidator):
    """Pydantic model to validate all payload key in input."""

    data: DerCndPrDataValidator
