"""Contains the Before class for the DerCndPr spider."""

from src.credentials.cpf_cnpj import CPFor<PERSON>NPJ
from src.spiders.before_spider import SpiderBefore
from src.spiders.autos_der_cnd_pr.schemas import DerCndPrPayload
from src.spiders.autos_der_cnd_pr.validators import DerCndPrPayloadValidator


class Before(SpiderBefore):
    """Class to execute validation and transformation before the spider process."""

    data_validation_model: type[DerCndPrPayloadValidator] = DerCndPrPayloadValidator

    @classmethod
    def transform_payload(cls, payload: DerCndPrPayloadValidator) -> DerCndPrPayload:
        """Normalize the payload.

        Args:
            payload (DerCndPrPayloadValidator): Payload to be normalized.

        Returns:
            DerCndPrPayload: Normalized payload.

        """
        model_json = payload.model_dump()
        model_json["data"]["cpf_cnpj"] = CPForCNPJ.from_value(payload.data.cpf_cnpj)
        return DerCndPrPayload(**model_json)
