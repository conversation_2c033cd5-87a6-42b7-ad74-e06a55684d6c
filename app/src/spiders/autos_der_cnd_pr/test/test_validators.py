import pytest
from pydantic import ValidationError

from src.spiders.autos_der_cnd_pr.validators import DerCndPrDataValidator, DerCndPrPayloadValidator


@pytest.fixture
def valid_der_cnd_pr_data():
    return {
        "cpf_cnpj": "12345678000195",
    }


class TestDerCndPrDataValidator:
    def test_creates_instance_with_valid_data(self, valid_der_cnd_pr_data):
        instance = DerCndPrDataValidator(**valid_der_cnd_pr_data)
        assert instance.cpf_cnpj == valid_der_cnd_pr_data["cpf_cnpj"]
        assert hasattr(instance, "start_date")
        assert hasattr(instance, "end_date")
        assert hasattr(instance, "get_boletos")
        assert hasattr(instance, "get_other_files")
        assert hasattr(instance, "capture_screenshots")

    def test_no_cpf_cnpj(self, valid_der_cnd_pr_data):
        del valid_der_cnd_pr_data["cpf_cnpj"]
        with pytest.raises(ValidationError) as excinfo:
            DerCndPrDataValidator(**valid_der_cnd_pr_data)
        error = excinfo.value.errors()[0]
        assert 'cpf_cnpj' in error['loc']
        assert error['msg'] == 'Field required'


class TestDerCndPrPayloadValidator:
    @pytest.fixture
    def valid_der_cnd_pr_payload(self, valid_der_cnd_pr_data):
        return {
            "data": valid_der_cnd_pr_data,
            "constraints": [],
            "target": [],
        }

    def test_creates_instance_with_valid_data(self, valid_der_cnd_pr_payload):
        instance = DerCndPrPayloadValidator(**valid_der_cnd_pr_payload)
        assert instance.data.cpf_cnpj == valid_der_cnd_pr_payload["data"]["cpf_cnpj"]
        assert instance.constraints == valid_der_cnd_pr_payload["constraints"]
        assert instance.target == valid_der_cnd_pr_payload["target"]
