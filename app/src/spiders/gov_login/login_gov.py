import ssl
import random
import base64
import asyncio
import tempfile
from enum import Enum
from hashlib import md5
from typing import Generator
from contextlib import contextmanager
from cryptography.hazmat.primitives.serialization.pkcs12 import load_key_and_certificates
from cryptography.hazmat.primitives.serialization import Encoding, PrivateFormat, NoEncryption

import aiohttp
from bs4 import BeautifulSoup

from src.credentials.cpf import CPF
from src.utils.cookies import AiohttpCookiesUtils
from src.utils.logger import LoggerFactory
from src.services.redis import RedisService
from src.utils.agent_data import create_agent_headers
from src.errors import CaptchaError, WrongCredentials, UndefinedError
from src.services.captcha.smart_captcha import SmartCaptchaService, SmartHcaptchaType
from src.utils.proxy import BrightDataProxy, ProxyZone


@contextmanager
def pfx_to_pem(pfx: bytes, password: str | bytes) -> Generator[str, None, None]:
    """Creates a temporary file with the PEM certificate and private key from a PFX file

    Args:
        pfx (bytes): PFX file content
        password (str | bytes): Password to decrypt the PFX file

    Returns:
        str: Path to the temporary file created
    """

    password = password.encode() if isinstance(password, str) else password
    private_key, main_cert, add_certs = load_key_and_certificates(pfx, password)

    with tempfile.NamedTemporaryFile(suffix='.pem', delete_on_close=False) as cert_file:
        cert_file.write(private_key.private_bytes(Encoding.PEM, PrivateFormat.PKCS8, NoEncryption()))
        cert_file.write(main_cert.public_bytes(Encoding.PEM))
        for ca in add_certs:
            cert_file.write(ca.public_bytes(Encoding.PEM))

        cert_file.close() # Close file to allow reading
        yield cert_file.name


class GovBrLoginType(Enum):
    CPF = 'CPF'
    CERTIFICATE = 'CERTIFICATE'


class GovLogin:
    MAX_CAPTCHA_RETRIES = 3

    def __init__(
            self,
            *,
            login_type: GovBrLoginType,
            cpf: CPF | None = None,
            password: str | None = None,
            certificate: bytes | str | None = None
    ):
        self._logger = LoggerFactory.get_logger(self.__class__.__name__)

        if login_type not in GovBrLoginType:
            class_module_as_str = f"{GovBrLoginType.__module__}.{GovBrLoginType.__qualname__}"
            raise ValueError(f'login_type must be an instance of {class_module_as_str} enum')

        if login_type == login_type.CPF:
            if not cpf or not password:
                raise ValueError("cpf and password are required for login with cpf")
            if not isinstance(cpf, CPF):
                self._logger.warning(
                    f'cpf must be an instance of %s. Casting type... Please, provide a correct type next time',
                    CPF.__mro__[0]
                )
                cpf = CPF(str(cpf))
        if login_type == login_type.CERTIFICATE:
            if not certificate or not password:
                raise ValueError('certificate and password are required for login with certificate')
            certificate = base64.b64decode(certificate) if isinstance(certificate, str) else certificate

        self.captcha_retries = self.MAX_CAPTCHA_RETRIES
        self.session: aiohttp.ClientSession | None = None
        self.login_type = login_type
        self.cpf = cpf
        self.password = password
        self.certificate = certificate

        if self.login_type == GovBrLoginType.CPF:
            self._cache_key = f'gov_br_login_data_{self.cpf.value}'
        elif self.login_type == GovBrLoginType.CERTIFICATE:
            certificate_hash = md5(self.certificate).hexdigest()
            self._cache_key = f"gov_br_login_data_certificate_{certificate_hash}"

    async def execute(self):
        cached_data = RedisService.get(self._cache_key)
        if cached_data:
            if 'wrong_password' in cached_data:
                raise WrongCredentials(message='Usuário e/ou senha inválidos', details='Usuário e/ou senha inválidos')
            self._logger.debug('Login data found in cache')
            return cached_data

        self._logger.debug('Starting login process')
        proxy = BrightDataProxy(ProxyZone.RESIDENTIAL, country_br=True)
        proxy = proxy.as_dict()

        async with aiohttp.ClientSession(
            headers=create_agent_headers(),
            proxy=f"{proxy['server']}:{proxy['port']}" ,
            proxy_auth=aiohttp.BasicAuth(proxy['username'], proxy['password']),
        ) as session:
            self.session = session
            return await self._execute()

    async def _execute(self):
        base_url = 'https://sso.acesso.gov.br/'
        self._logger.debug('Getting login page')
        await self.session.get(base_url)  # This is only to get cookies
        # This was extracted from main*.js from site - Original JS: Math.floor(99999 * Math.random())
        random_number = round(random.random() * 99999)
        query_params ={
            'response_type': 'code',
            'client_id': 'portal-logado.estaleiro.serpro.gov.br',
            'scope': 'openid+(email/phone)+profile+govbr_empresa+govbr_confiabilidades+govbr_wallet+govbr_notificacao',
            'redirect_uri': 'https://servicos.acesso.gov.br/login',
            'nonce': random_number,
            # This is a semi-fixed value, is not clear what it is. Is a string like "/login" converted with JS Buffer
            'state': 'eyJwYXRoIjoiLyJ9'
        }
        url = base_url + 'authorize'
        response = await self.session.get(url, params=query_params)

        soap = BeautifulSoup(await response.text(), 'html.parser')
        csrf_token = soap.select_one('input[name="_csrf"]').get('value')
        self._logger.debug('CSRF token obtained')

        hcaptcha_token = SmartCaptchaService.hcaptcha(SmartHcaptchaType.GOV_BR_LOGIN)[0]

        partial_form = {'_csrf': csrf_token, 'h-captcha-response': hcaptcha_token}
        if not hcaptcha_token:
            raise CaptchaError(message='Não foi possível resolver o captcha', details='Captcha not solved')

        if self.login_type == GovBrLoginType.CPF:
            return await self._cpf_login(partial_form, response.url)
        elif self.login_type == GovBrLoginType.CERTIFICATE:
            return await self._certificado_login(partial_form, response.url)

    async def _cpf_login(self, partial_form: dict, url: aiohttp.client.URL):
        form = {
            **partial_form,
            'accountId': self.cpf.formatted,
            'operation': 'enter-account-id',
        }
        response = await self.session.post(url, data=form)
        return await self._cpf_login__redirect(response)

    async def _cpf_login__redirect(self, response: aiohttp.ClientResponse):
        if response.status == 400:
            response_text = await response.text()
            soap = BeautifulSoup(response_text, 'html.parser')
            error_message = soap.select_one('div.erro > p').text.strip()
            if 'captcha inválido' in error_message.lower():
                if self.captcha_retries > 0:
                    self._logger.warning('Invalid captcha. Retrying...')
                    self.captcha_retries -= 1
                    return await self._execute()
                raise CaptchaError(
                    message='Não foi possível resolver o captcha',
                    details=f'Captcha not solved after {self.MAX_CAPTCHA_RETRIES} retries'
                )

        if response.status in {301, 302}:
            url = response.headers.get('Location')
            if 'cadastro' in url:
                msg = 'CPF não cadastrado.'
                raise WrongCredentials(message=msg, details=msg)
            response = await self.session.get(url)
            return await self._cpf_login__redirect(response)

        soap = BeautifulSoup(await response.text(), 'html.parser')
        token = soap.select_one('input[name="token"]').get('value')
        csrf_token = soap.select_one('input[name="_csrf"]').get('value')

        form = {
            'token': token,
            '_csrf': csrf_token,
            'operation': 'enter-password',
            'password': self.password
        }
        response = await self.session.post(response.url, data=form)
        return await self._cpf_login__redirect_after_password(response)

    async def _cpf_login__redirect_after_password(self, response: aiohttp.ClientResponse):
        if response.status == 400:
            response_text = await response.text()
            soap = BeautifulSoup(response_text, 'html.parser')
            error_element = soap.select_one('div.erro > p') or soap.select_one('.warning > p')
            if not error_element:
                raise WrongCredentials(message="Não foi possivel fazer o login no site", details=response_text)

            error_message = error_element.text.strip()
            if 'Usuário e/ou senha inválidos'.lower() in error_message.lower():
                self._logger.error(error_message)
                RedisService.set(self._cache_key, {'wrong_password': self.password})
                raise WrongCredentials(message=error_message, details=error_message)

            raise UndefinedError(
                message=error_message,
                details="Error received at try to login in Gov.br after password. HTML: %s" % response_text
            )

        if response.status in {301, 302}:
            url = response.headers.get('Location')
            response = await self.session.get(url)
            return await self._cpf_login__redirect_after_password(response)

        return await self._get_session_data(response.url)

    async def _certificado_login(self, partial_form: dict, url: aiohttp.client.URL):
        self._logger.debug('Logging with certificate')
        form = {
            **partial_form,
            'operation': 'login-certificate',
            'accountId': ''
        }
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'pt-PT,pt;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://sso.acesso.gov.br',
            'Pragma': 'no-cache',
            'Referer': 'https://sso.acesso.gov.br/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
        }
        params = {
            'client_id': 'portal-logado.estaleiro.serpro.gov.br',
            'authorization_id': url.query.get("authorization_id")
        }
        req_url = aiohttp.client.URL('https://certificado.sso.acesso.gov.br/login').update_query(params)

        with pfx_to_pem(self.certificate, self.password) as cert_file:
            ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
            ssl_context.load_verify_locations(cafile=cert_file)
            ssl_context.load_cert_chain(certfile=cert_file, keyfile=cert_file)
            for i in range(3):  # for some reason, sometimes the first time it fails
                try:
                    response = await self.session.post(req_url, data=form, headers=headers, ssl=ssl_context, timeout=60)
                except aiohttp.ClientConnectorError as e:
                    self._logger.error(f'Error on certificate login: {e}')
                    continue
                else:
                    break
            await asyncio.sleep(.1)  # Wait a little to avoid connection reset by peer
        return await self._get_session_data(response.url)

    async def _get_session_data(self, last_url: aiohttp.client.URL):
        self._logger.debug('Getting JWT token')
        code = last_url.query.get('code')
        state = last_url.query.get('state')

        url = f'https://servicos.acesso.gov.br/api/login/{code}'
        params = {
            'urlRetorno': 'https://servicos.acesso.gov.br/login',
            'state': state
        }
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Referer": str(last_url)
        }
        response = await self.session.get(url, params=params, headers=headers)
        _json = await response.json()

        cookies = await AiohttpCookiesUtils.cookiejar_to_list(self.session.cookie_jar)
        result = {
            'storage': _json,
            'cookies': cookies
        }
        self.save_data(result)
        return result

    def save_data(self, data: dict[str, dict]) -> None:
        self._logger.debug('Saving login data to cache')
        RedisService.set(self._cache_key, data, expiration=1800)
