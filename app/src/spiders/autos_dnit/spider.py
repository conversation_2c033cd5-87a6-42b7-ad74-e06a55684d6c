"""Module to scrape fines from DNIT website."""

import json
import base64
from uuid import uuid4
from pathlib import Path
from urllib.parse import parse_qs, urlparse

from requests import Session

from src.errors import UndefinedError, InvalidRequestParams
from src.objects import TicketObject, DocumentObject, DocumentMetaObject
from src.utils.proxy import ProxyZone, BrightDataProxy
from src.utils.logger import LoggerFactory
from src.utils.cookies import RequestsCookiesUtils
from src.services.redis import RedisService
from src.services.channel import Channel, Message
from src.utils.agent_data import create_agent_headers
from src.spiders.gov_login import GovBrLoginType, execute_gov_login
from src.services.file_service import FileService
from src.spiders.autos_dnit.schemas import DnitPayload
from src.spiders.autos_dnit.enquadramento import enquadramentos


path = Path(__file__).parent / "teste"


class AutosDnitSpider:
    """Spider to scrape fines from DNIT website."""

    def __init__(self, channel: Channel, file_service: FileService, payload: DnitPayload) -> None:
        """Initialize the spider.

        Args:
            channel (Channel): Channel to send messages.
            file_service (FileService): File service to save files.
            payload (DnitPayload): Payload with data for the spider.

        """
        self._channel = channel
        self._file_service = file_service

        self.cpf = payload.data.cpf
        self.senha = payload.data.senha
        self.cnpj = payload.data.cnpj
        self.get_boletos = payload.data.get_boletos
        self.get_other_files = payload.data.get_other_files
        self.constraints = payload.constraints

        self.redis_key = f"autos_dnit_{self.cpf.value}"

        self.enquadramentos = enquadramentos
        self.session: Session = Session()
        self.session.headers.update(create_agent_headers())

        proxy = BrightDataProxy(ProxyZone.RESIDENTIAL, country_br=True)
        proxy_string = proxy.as_string()
        self.session.proxies.update({"http": proxy_string, "https": proxy_string})

        self.empresas: list[dict] = []
        self.token_headers = {}
        self.token: str | None = None
        self.files_referer: str | None = None

        self.logger = LoggerFactory.get_logger(__name__)

    def execute(self) -> None:
        cached_data = RedisService.get(self.redis_key)
        if cached_data:
            self.logger.info("Using dnit cached data")
            self.token = cached_data["token"]
            cookies = cached_data["cookies"]
            self.empresas = cached_data["empresas"]
            self.token_headers = {"Authorization": f"Bearer {self.token}"}
            self.session.cookies.update(RequestsCookiesUtils.list_to_cookiejar(cookies))
            return self.get_empresa()

        self.logger.info("No cached data found. Getting gov login data")
        login_data = execute_gov_login(GovBrLoginType.CPF, cpf=self.cpf, password=self.senha)
        cookies = login_data["cookies"]
        jar = RequestsCookiesUtils.list_to_cookiejar(cookies)
        self.session.cookies.update(jar)

        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,"
                      "*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7,es;q=0.6",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://servicos.dnit.gov.br",
            "Pragma": "no-cache",
            "Referer": "https://servicos.dnit.gov.br/multas/",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
        }
        url = "https://servicos.dnit.gov.br/auth-sior/govbr?urlRetorno=https://servicos.dnit.gov.br/multas/signin-govbr"
        response = self.session.post(url, headers=headers)
        query = parse_qs(urlparse(response.url).query)
        if "sessionId" in query:
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "pt-PT,pt;q=0.9",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "application/json;charset=utf-8",
                "Origin": "https://servicos.dnit.gov.br",
                "Pragma": "no-cache",
                "Referer": response.url,
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "X-API-Version": "0.0",
                "X-Requested-With": "XMLHttpRequest",
            }
            url = "https://servicos.dnit.gov.br/auth-sior/govbr-user-data"
            response = self.session.post(url, headers=headers, params={"guid": query["sessionId"][0]})
            required_data = response.json()
        else:
            # get data for next request
            query = parse_qs(urlparse(response.url).query)
            required_data = {key: query.get(key, [""])[0] for key in query}

        # Required for pdf download
        self.token = required_data["token"]
        # header required to get files in the future
        self.token_headers = {"Authorization": f"Bearer {self.token}"}

        # Need to set a cookie with the data from the response
        keys_for_cookies = ["cpf", "name", "email", "id", "expiration"]
        portal_data_cookies = {key: required_data[key] for key in keys_for_cookies}
        # this key can be birthday or updateBirthday, depends on `if` condition above
        portal_data_cookies["updateBirthday"] = required_data.get("updateBirthday") or required_data.get("birthday")

        self.session.cookies.update({"portalData": json.dumps(portal_data_cookies)})

        # get cookies from .dnit.gov.br domain
        cookies = RequestsCookiesUtils.cookiejar_to_list(self.session.cookies)
        cookies = list(filter(lambda x: x["domain"] in {".dnit.gov.br", "servicos.dnit.gov.br"}, cookies))

        empresas = base64.b64decode(required_data.get("empresas", b"")).decode()
        if not empresas:
            msg = "Houve um erro ao buscar as empresas autorizadas do usuario"
            raise UndefinedError(msg)

        self.empresas = json.loads(empresas)
        to_cache = {"token": self.token, "cookies": cookies, "empresas": self.empresas}
        RedisService.set(self.redis_key, to_cache, expiration=60 * 60 * 24)
        return self.get_empresa()

    def get_empresa(self) -> None:
        """Get the company data for the user and check if it is allowed to access fines."""
        empresa = list(filter(lambda x: x["Cnpj"] == self.cnpj.value, self.empresas))
        if not empresa:
            msg = "Empresa não permitida para o usuario"
            raise InvalidRequestParams(msg)

        [empresa, *_] = empresa
        self.get_infracoes(empresa)

    def get_infracoes(self, empresa: dict) -> None:
        """Get infractions for a given company. Do pagination and process fines."""
        empresa_name = empresa["RazaoSocial"]
        empresa_cnpj = empresa["Cnpj"]
        page_size = 5  # Original value in website is 5. (Can't be changed on website UI)

        has_more = True
        iteration = 0
        while has_more:
            self.logger.debug("Getting infractions. Page: %d", iteration + 1)
            query_params = {
                "cnpj": empresa_cnpj,
                "razaoSocial": empresa_name,
                "pageSize": page_size,
                "skip": iteration * page_size,
                "ordenacaoCrescente": False
            }
            response_inf = self.session.get(
                "https://servicos.dnit.gov.br/services-sior/portal-multas/infracoes/cnpj",
                params=query_params,
                headers=self.token_headers
            )

            response_json = response_inf.json()
            fines = response_json["infracoes"]

            if iteration == 0 and not fines:
                self.logger.info("No fines found")
                return

            # set referer for files requests. Setted only on first request
            if not self.files_referer:
                self.files_referer = response_inf.request.url

            fines = filter(self._filter_fine, fines)
            for fine in fines:
                self.process_fine(fine)

            has_more = response_json["hasMore"]
            iteration += 1

    def process_fine(self, fine: dict) -> None:
        """Process fine, downloading documents and send data.

        Args:
            fine (dict): Fine data.

        """
        documents = []
        if fine.get("indicadorGuiaPagamento"):
            try:
                pdf = self._get_gru_pdf(fine)
                if pdf:
                    file_id = f"{uuid4()}.pdf"
                    self._file_service.save_file(file_id, pdf)
                    doc = DocumentObject(
                        meta=DocumentMetaObject(
                            file_id=file_id,
                            kind="boleto",
                        )
                    )
                    documents.append(doc)
            except (Exception,) as e:
                self.logger.warning('Não foi possível baixar boleto. Error: %s', e)

        try:
            np = self._get_np_pdf(fine)
            if np:
                file_id = f"{uuid4()}.pdf"
                self._file_service.save_file(file_id, np)
                doc = DocumentObject(
                    meta=DocumentMetaObject(
                        file_id=file_id,
                        kind="notificacao",
                    )
                )
                documents.append(doc)
        except (Exception,) as e:
            self.logger.warning('Não foi possível baixar a notificação. Error: %s', e)

        ticket = TicketObject(data=fine, documents=documents)
        message = Message(data=ticket)
        self._channel.send(message)

    def _filter_fine(self, fine: dict) -> bool:
        """Filter fines based on constraints and enquadramentos.

        Args:
            fine (dict): Fine data.

        Returns:
            bool: True if fine should be processed, False otherwise.

        """
        if fine["numeroAuto"] in self.constraints:
            return False
        return fine["enquadramento"] not in self.enquadramentos

    def _get_gru_pdf(self, fine: dict) -> bytes | None:
        """Download fine ticket (boleto) and return it as bytes.

        Args:
            fine (dict): Fine data.

        Returns:
            bytes | None: PDF content or None if an error occurred.

        """
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,"
                      "image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Pragma": "no-cache",
            "Referer": self.files_referer,
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
        }

        params = {
            "codigo": fine["codigoProcessoEncrypted"],
            "numeroCnpj": self.cnpj.value,
            "auto": fine["numeroAuto"],
            "nomeUsuario": f"{self.cpf.formatted} (Portal Multas)",
            "token": self.token,
        }
        url = "https://servicos.dnit.gov.br/services-sior/gru/infracao/emitir"
        response = self.session.get(url, headers=headers, params=params)

        if not response.ok or "pdf" not in response.headers.get("Content-Type", ""):
            self.logger.error("Error getting GRU PDF: %s", response.status_code)
            return None

        return response.content

    def _get_np_pdf(self, fine: dict) -> bytes | None:
        """Download fine penalty notification (NP) and return it as bytes.

        Args:
            fine (dict): Fine data.

        Returns:
            bytes | None: PDF content or None if an error occurred.

        """
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,"
                      "*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Pragma": "no-cache",
            "Referer": self.files_referer,
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
        }
        params = {
            "codigo": fine["npCodigoProcesso"],
            "indicadorComprovacao": fine["codigoInfracaoIndicadorComprovacao"],
            "codigoTipoPenalidade": fine["codigoTipoPenalidade"],
            "auto": fine["numeroAuto"],
            "token": self.token,
        }

        url = "https://servicos.dnit.gov.br/services-sior/relatorio/infracao/np"
        response = self.session.get(url, headers=headers, params=params)

        if not response.ok or "pdf" not in response.headers.get("Content-Type", ""):
            self.logger.error("Error getting NP PDF: %s", response.status_code)
            return None

        return response.content


if __name__ == "__main__":
    from src.spiders.orchestrator import Orchestrator

    payload = {
        "uuid": str(uuid4()),
        "spider_name": "autos_dnit",
        "delivery": {
            "type": "AWS_SQS",
            "queue_name": "development_spider_to_maestro",
        },
        "constraints": [],
        "data": {
            "cnpj": "61460325000494",
            "senha": "#Brobot@Conta@G1986",
            "cpf": "22379478805",
            "start_date": None,
            "end_date": None,
            "capture_screenshot": False,
            "get_other_files": True,
            "get_boletos": True,
        },
        "target": ["multa_embarcador"],
    }

    Orchestrator.process(payload)
