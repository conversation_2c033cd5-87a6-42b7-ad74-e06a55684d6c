from src.credentials.cnpj import CNPJ
from src.credentials.cpf import CPF
from src.spiders.autos_dnit.schemas import DnitPayload
from src.spiders.autos_dnit.validators import DnitPayloadValidator
from src.spiders.before_spider import SpiderBefore


class Before(SpiderBefore):
    data_validation_model: type[DnitPayloadValidator] = DnitPayloadValidator

    @classmethod
    def transform_payload(cls, payload: DnitPayloadValidator) -> DnitPayload:
        """Normalize the payload"""

        model_json = payload.model_dump()
        model_json["data"]["cpf"] = CPF(payload.data.cpf)
        model_json['data']['cnpj'] = CNPJ(payload.data.cnpj)
        return DnitPayload(**model_json)