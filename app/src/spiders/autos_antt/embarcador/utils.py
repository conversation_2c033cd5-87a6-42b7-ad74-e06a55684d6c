"""Contais some utils functions to create forms and extract some secondary data from the html."""

import re
from urllib.parse import unquote

from bs4 import BeautifulSoup

from src.utils.strings import remove_diacritics
from src.spiders.autos_antt.utils import extract_script_manager_main_hidden_field


def get_avaliables_multa_values(soup: BeautifulSoup) -> dict[str, int]:
    """Return a dict with the values of the select. Key is the text and value is the select value.

    Args:
        soup (BeautifulSoup): The parsed html.

    Returns:
        dict[str, int]: A dict with the values of the select. Key is the text and value is the select value.

    """
    elem_name = "ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ddlTipoMulta"
    select = soup.find("select", {"name": elem_name})
    res = {}
    for option in select.select('option:not([value=""])'):
        key = remove_diacritics(option.text.strip().lower())
        res[key] = int(option.get("value"))
    return res


def get_avaliables_representados(soup: BeautifulSoup) -> set[str]:
    """Find and return a set with the values of representados avaliables in html.

    Args:
        soup (BeautifulSoup): The parsed html.

    Returns:
        set[str]: A set with the values of representados avaliables in html.

    """
    element_selector = "#ContentPlaceHolderCorpo_ContentPlaceHolderCorpo_ContentPlaceHolderCorpo_ddlRepresentado"
    select_element = soup.select_one(element_selector)
    if not select_element:
        return set()
    options = select_element.select('option:not([value=""])')
    return {option.get("value") for option in options}


def build_multas_form(
        html: str | BeautifulSoup,
        fine_code: int,
        representado: str | None,
        page: int | None = None
) -> dict[str, str]:
    """Return a dict with the fields to be sent in the fines form.

    Args:
        html (str | BeautifulSoup): The html text or a BeautifulSoup object.
        fine_code (int): The code of the fine.
        representado (str | None): The representado value.
        page (int | None): The page to be requested. If None, the form will be built to request the first page.

    Returns:
        dict[str, str]: The form data

    Obs:
        When no page is passed, the form will be built to request the first page. This is like click in "Pesquisar"
        after selecting the fine type.

        For pagination, the page number must be passed as the `page` argument.

    """
    if not isinstance(html, BeautifulSoup):
        html = BeautifulSoup(html, "html.parser")

    # This example is to be used as a reference to build the form and as guide to debuggig
    example_form = {
        "ctl00$ctl00$ctl00$ScriptManagerMain":
            "ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$updInfrator"
            "|ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$"
            "ucPaginador$ucPaginador_rptPages$ctl01$lbPage",
        "ScriptManagerMain_HiddenField": ";;AjaxControlToolkit, Version=4.1.60919.0, Culture=neutral, "
                                         "PublicKeyToken=28f01b0e84b6d53e:pt-BR:ab75ae50-1505-49da-acca-8b96b908cb1a:"
                                         "5546a2b:475a4ef5:d2e10b12:effe2a26:37e2e5c9:1d3ed089:751cdd15:dfad98a5:"
                                         "497ef277:a43b07eb:3cf12cf1;",
        "ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ddlTipoMulta": 12,
        "ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$txtNumeroAuto": "",
        "__EVENTTARGET": "ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$"
                         "ucPaginador$ucPaginador_rptPages$ctl01$lbPage",
        "__EVENTARGUMENT": "",
        "__LASTFOCUS": "",
        "__VIEWSTATE": "a0uzq68pZa1aUBKKak5xM",
        "__VIEWSTATEGENERATOR": "F8D50474",
        "__EVENTVALIDATION": "u0q0ZLuQp0euUe22Xg96Ew",
        "__VIEWSTATEENCRYPTED": "",
        "__ASYNCPOST": True
    }

    res = {}
    for key in example_form:
        tag = html.find("input", {"name": key})
        if tag:
            res[key] = tag.get("value", "")
        else:
            res[key] = ""

    res["__ASYNCPOST"] = True
    res["ScriptManagerMain_HiddenField"] = extract_script_manager_main_hidden_field(html)
    res[("ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$"
         "ContentPlaceHolderCorpo$ddlTipoMulta")] = str(fine_code)
    if representado:
        res[("ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$"
             "ContentPlaceHolderCorpo$ddlRepresentado")] = representado

    if not page or page == 1:  # First page
        res[("ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$"
             "ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$btnPesquisar")] = "Pesquisar"
        res["ctl00$ctl00$ctl00$ScriptManagerMain"] = ("ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$"
                                                      "ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$updInfrator"
                                                      "|ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$"
                                                      "ContentPlaceHolderCorpo$ContentPlaceHolderCorpo"
                                                      "$btnPesquisar")
    else:
        element = html.find("a", string=page)
        if element:
            href = element.get("href")
            value = re.search(r"javascript:__doPostBack\('(.*)',''\)", href).group(1)

            res["__EVENTTARGET"] = value
            res["ctl00$ctl00$ctl00$ScriptManagerMain"] = ("ctl00$ctl00$ctl00$ContentPlaceHolderCorpo$"
                                                          "ContentPlaceHolderCorpo$ContentPlaceHolderCorpo$updInfrator|"
                                                          + value)

    return res
