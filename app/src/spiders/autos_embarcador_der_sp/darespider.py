

from requests import Session
from bs4 import BeautifulSoup
import re

from src.utils.agent_data import create_agent_headers
from src.utils.logger import LoggerFactory


class DareSpider:
    HOME_URL = "http://www.edare.der.sp.gov.br/der_edare_web/pages/DER_eDARE/DARE.aspx"
    BOLETO_URL = 'http://www.edare.der.sp.gov.br/der_edare_web/pages/DER_eDARE/RESPDARE.aspx'

    def __init__(self, session: Session, placa: str, ait: str):
        """
        Initialize an instance of DareSpider.

        Args:
            session (Session): The session object for making HTTP requests.
            placa (str): The license plate number.
            ait (str): The AIT (Auto de Infração) number.
        """
        if not all([session, placa, ait]):
            raise ValueError(f"session, placa and ait are required for initializing {self.__class__.__name__}.")
        self.session = session
        self.placa = placa
        self.ait = ait.replace(r"\W", "")
        self.logger = LoggerFactory.get_logger(self.__class__.__name__)

    def _get_homepage(self):
        self.logger.debug("Getting homepage")
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Pragma": "no-cache",
            "Upgrade-Insecure-Requests": "1",
        }
        response = self.session.get(self.HOME_URL, headers=headers, verify=False)
        return response.text

    def get_dare(self) -> bytes | None:
        """
        Get the DARE (Documento de Averbação de Responsabilidade) for the given license plate and AIT.
        """
        self.logger.info("Getting DARE")

        html = self._get_homepage()
        soup = BeautifulSoup(html, 'html.parser')
        hidden_inputs = {
            input_tag.get('name'): input_tag.get('value', '')
            for input_tag in soup.find_all('input', type='hidden')
        }
        form_data = {
            **hidden_inputs,
            'ctl00$ePortalContent$txtCNPJCPF': '',
            'ctl00$ePortalContent$txtPLACA': self.placa,
            'ctl00$ePortalContent$txtAIT': self.ait,
            'ctl00$ePortalContent$checkAceite': 'on',
            'ctl00$ePortalContent$EPortalSubmitButton1': 'Pesquisar'
        }
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,"
                      "*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "http://www.edare.der.sp.gov.br",
            "Pragma": "no-cache",
            "Referer": "http://www.edare.der.sp.gov.br/der_edare_web/pages/DER_eDARE/DARE.aspx",
            "Upgrade-Insecure-Requests": "1",
        }
        self.logger.debug("Sending form data")
        response = self.session.post(self.HOME_URL, headers=headers, data=form_data, verify=False)

        message_match = re.search(r"<script language=JavaScript>msg\('([^']+)'\)", response.text)
        if message_match:
            message = message_match.group(1)
            if "Serviço Temporariamente Indisponível" in message:
                self.logger.warning("Não foi possível obter o DARE: %s", message)
                return None
            elif 'Clique em 2ª via DARE' in message:
                pass
            else:
                self.logger.warning("Mensagem desconhecida: %s", message)

        soup = BeautifulSoup(response.text, 'html.parser')
        dare_button = (
            soup.find('input', {'value': '2ª via DARE'})
            or
            soup.find('input', {'value': 'Solicitar Nova DARE'})
        )
        if not dare_button:
            self.logger.warning("Não foi encontrado o botão para obter o DARE")
            return None

        form = soup.find('form', {'id': 'aspnetForm'})
        input_tags = form.find_all('input')
        inputs_to_exclude = {'ctl00$ePortalContent$btnNovaPesquisa', 'ctl00$ePortalContent$EPortalSubmitButton1'}

        form_data = {
            tag.get('name'): tag.get('value', '')
            for tag in input_tags if tag.get('name') not in inputs_to_exclude
        }

        form_data['__EVENTTARGET'] = dare_button.get('name', '')
        self.logger.debug("Enviando requisição para obter o DARE")
        dare_response = self.session.post(self.BOLETO_URL, data=form_data, headers=headers, verify=False)

        if dare_response.status_code == 200 and 'application/pdf' in dare_response.headers.get('Content-Type', ''):
            self.logger.info("DARE obtido com sucesso")
            return dare_response.content

        self.logger.warning("Não foi possível obter o DARE após a segunda requisição")
        return None


if __name__ == "__main__":
    from requests import Session

    LoggerFactory.define_handler("test")

    s = Session()
    s.headers.update(create_agent_headers())

    # dare = DareSpider( s,"EOS0E13", "1G4187282")
    # dare = DareSpider( s,"JXA0732", "1Y 3935721")
    dare = DareSpider( s,"ADM3008", "1L 5902291")
    d = dare.get_dare()
    with open('dare.pdf', 'wb') as f:
        f.write(d)





