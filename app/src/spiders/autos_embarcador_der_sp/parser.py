import re

from bs4 import BeautifulSoup, Tag
from src.utils.strings import remove_diacritics

from pprint import pprint

class AutosEmbarcadorDerSpParser:

    @staticmethod
    def extract_infracoes(html: str | BeautifulSoup):
        if isinstance(html, str):
            html = BeautifulSoup(html, "html.parser")

        requests_verification_token = html.select_one("input[name='__RequestVerificationToken']").get("value")

        infracoes_table = html.select_one("table.table.myTable")
        header_keys = [remove_diacritics(th.text) for th in infracoes_table.select("thead th")]

        result = []
        trs = infracoes_table.select("tbody tr")
        for row in trs:
            values = [td.text.strip() for td in row.select("td")]
            row_data = dict(zip(header_keys, values))
            row_data['ait'] = re.sub(r'\W', '', row_data['ait'])  # This field has many special characters like \n

            details_button_div = row.select_one('td:nth-child(1) div.oculta')
            details_form_data = {inp.get('name'): inp.get('value') for inp in details_button_div.select('input')}
            details_form_data.update({'__RequestVerificationToken': requests_verification_token})

            row_data['__details_form_data__'] = details_form_data

            pdf_button_div = row.select_one("td:last-child div.oculta")
            details_form_data = {inp.get("name"): inp.get("value") for inp in pdf_button_div.select("input")}
            details_form_data.update({"__RequestVerificationToken": requests_verification_token})
            row_data['__pdf_form_data__'] = details_form_data

            result.append(row_data)

        return result

    @classmethod
    def extract_details(cls, html: BeautifulSoup | str):
        if isinstance(html, str):
            html = BeautifulSoup(html, "html.parser")

        res = {}

        expected_tables_names = [
            "IDENTIFICAÇÃO DA AUTUAÇÃO",
            "IDENTIFICAÇÃO DO VEÍCULO",
            "IDENTIFICAÇÃO DO LOCAL DE COMETIMENTO DA INFRAÇÃO",
        ]
        for table_label in expected_tables_names:
            el = html.find("p", string=table_label)
            table = el.find_next("table")
            current_result = cls.extract_table_as_dict(table)

            res[remove_diacritics(table_label)] = current_result

        tip = {}
        s = 'TIPIFICAÇÃO DA INFRAÇÃO'
        el = html.find("p", string=re.compile(s))
        table_below = el.find_all_next("table")
        for  table in table_below:
            current_result = cls.extract_table_as_dict(table)
            tip.update(current_result)

        base_legal = tip.pop("base_legal", "")
        artigo = tip.pop("artigo", "")
        inciso = tip.pop("inciso", "")
        base_legal_str = f"{base_legal} Artigo: {artigo} Inciso: {inciso}"
        tip['base_legal'] = base_legal_str
        res[remove_diacritics(s)] = tip

        return res

    @staticmethod
    def extract_table_as_dict(table: Tag):
        header_keys = [remove_diacritics(th.text) for th in table.select("thead th")]
        tr = table.select_one("tbody tr:nth-child(1)")
        tds = tr.select("td:not(.oculta)")
        values = map(lambda x: re.sub(r"\s+", " ", x.text).strip() or None, tds)

        return dict(zip(header_keys, values))


if __name__ == '__main__':
    with open('main.html', 'r', encoding='utf8') as f:
        html = f.read()
    multas = AutosEmbarcadorDerSpParser.extract_infracoes(html)
    pprint(multas, sort_dicts=False, width=200, compact=True)

    # import json
    # with open('res.json', 'w', encoding='utf8') as f:
    #     json.dump(multas, f, ensure_ascii=False, indent=4)
