import os

import requests as r
import requests.exceptions
from typing import Optional

host = os.environ.get("HOST_API_SERVICE", "http://api-service:8000")


def get_hcaptcha_gov_token():
    """Get a hcaptcha token from the gov.br site. The provider of this token is the api-service."""
    response = r.get(f"{host}/hcaptchagovtoken")
    try:
        return response.json()
    except requests.exceptions.JSONDecodeError:
        return {"error": "Error decoding JSON", "response": response.text}


def get_hcaptcha_serpro_radar():
    """Get a hcaptcha token from the serpro_radar site. The provider of this token is the api-service."""
    response = r.get(f"{host}/hcaptchaserprotoken")
    return response.json()



def get_logged_detran_se(renavam: str, codigo_de_seguranca: Optional[str] = None):
    """Get a hcaptcha token from the serpro_radar site. The provider of this token is the api-service."""
    params = {"renavam": renavam}
    if codigo_de_seguranca:
        params["codigo_de_seguranca"] = codigo_de_seguranca
    response = r.get(f'{host}/logindetran', params=params)
    return response.json()