import random

import requests as r

urls = [
    "https://ifconfig.me",
    "https://icanhazip.com",
    "https://ipinfo.io/ip",
    "https://api.ipify.org",
    "https://ident.me",
    "https://ipecho.net/plain",
]


def _show_proxy_ip(proxy_url, logger=None):
    logger = logger.info if logger else print

    if proxy_url is None:
        logger("Proxy URL missing")
        return None

    # Shuffle URLs to randomize the order
    random.shuffle(urls)

    for url in urls:
        try:
            response = r.get(url, proxies={"http": proxy_url, "https": proxy_url})
            if response.status_code == 200:
                logger(f"IP from proxy: {response.text}")
                return response.text.strip()
        except Exception as e:
            logger(f"Unable to see IP on {url} service. Error: {e!s}")
