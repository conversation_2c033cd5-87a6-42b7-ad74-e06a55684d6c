import time
import signal

import load_queue
import load_spider_path

from process_messages import ProcessMessages
from job import Job

queue = load_queue.load()
spider_path = load_spider_path.load()


class Engine:
    runner = True

    def __init__(self):
        signal.signal(signal.SIGINT, self._stop)
        signal.signal(signal.SIGTERM, self._stop)

    def start(self):
        while self.runner:
            self._process()

    def _process(self):
        pm = ProcessMessages(queue)
        job = Job(spider_path)
        amount = pm.process(job)

        if amount == 0:
            self._sleep()

    def _sleep(self):
        for _ in range(10):
            if not self.runner:
                return
            print('#', end='')
            time.sleep(1)
        print('')

    def _stop(self, *args):
        print('')
        print('Shutting down...')
        self.runner = False
