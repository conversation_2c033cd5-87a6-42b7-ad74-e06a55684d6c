FROM python:3.8.10-alpine3.13

RUN apk --update add --no-cache gcc \
    musl-dev \
    gfortran \
    lapack \
    libexecinfo-dev \
    libgcc \
    libquadmath \
    libgfortran \
    g++ \
    poppler-utils \
    poppler-dev \
    openblas-dev \
    build-base \
    python3-dev \
    freetype-dev \
    openjpeg-dev \
    libffi-dev \
    cairo-dev \
    pango-dev \
    libstdc++ \
    libx11 \
    glib \
    libxrender \
    libxext \
    libintl \
    ttf-dejavu \
    ttf-droid \
    ttf-freefont \
    ttf-liberation \
    ttf-ubuntu-font-family \
    chromium \
    chromium-chromedriver \
    zbar \
    zbar-dev \
    xvfb-run

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    CHROME_DRIVER_PATH=/usr/bin/chromedriver

RUN chmod 777 /usr/bin/chromedriver  # seleniumwire needs to read chromedriver binary instead of just running it - 755 doesn't work

RUN pip install "setuptools<58" && pip install demjson

# Install library
RUN pip install --upgrade pip wheel \
    && pip install setuptools==70.0.0 \
    && pip install pandas==1.1.0 \
    && pip install pdftotext==2.1.4 \
    && pip install capsolver

# Set the working directory
WORKDIR /app

ADD requirements.txt .
# Need return to setuptools updated version for the next packages
RUN pip install -r requirements.txt

# On alpine static compiled patched qt headless wkhtmltopdf (46.8 MB).
COPY --from=madnight/alpine-wkhtmltopdf-builder:0.12.5-alpine3.10-606718795 \
    /bin/wkhtmltopdf /bin/wkhtmltopdf
RUN [ "$(sha256sum /bin/wkhtmltopdf | awk '{ print $1 }')" \
        == "06139f13500db9b0b4373d40ff0faf046e536695fa836e92f41d829696d6859f" ]

COPY --from=madnight/alpine-wkhtmltopdf-builder:0.12.5-alpine3.10-606718795 \
    /bin/wkhtmltoimage /bin/wkhtmltoimage

ENV APP_VERSION=v2

CMD ["python"]
