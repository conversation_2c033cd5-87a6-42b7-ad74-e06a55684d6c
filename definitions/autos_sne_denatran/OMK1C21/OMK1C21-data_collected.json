{"scrape_id": "OMK1C21-3cb3-48ba-bd35-OMK1C21", "scraper_name": "autos_sne_denatran", "cnpj": "", "files_count": 1, "screenshots_count": 0, "result": {"nao_pagas": [{"__boleto__": {"file_unavailable": "Órgão consultado não permitiu download do boleto neste momento. Por favor, aguarde 72 horas e tente novamente."}, "tipoInfracao": 2, "codigoOrgaoAutuador": "126200", "numeroAutoInfracao": "1C 0529578", "codigoInfracao": "7455", "descricaoInfracao": "TRANSITAR EM VELOCIDADE SUPERIOR A MAXIMA PERMITIDA EM ATE 20%", "descricaoOrgaoAutuador": "DER - SP", "codigoOrgaoCompetente": "126200", "descricaoOrgaoCompetente": "DER - SP", "dataInfracao": "2023-10-25T06:32:00Z", "localOcorrenciaInfracao": "SP  330     KM 350 METROS 350", "descricaoMunicipioInfracao": "SALES OLIVEIRA", "dataNotificacaoAutuacao": "2023-11-06T00:00:00Z", "dataNotificacaoPenalidade": "2024-01-02T00:00:00Z", "dataVencimentoPenalidade": "2024-02-14T00:00:00Z", "dataLimiteDefesaPrevia": "2023-12-13T00:00:00Z", "valorIntegralInfracao": 130.16, "codigoTipoAIT": 2, "urlOrgaoAutuador": "", "indicadorCondutor": 0, "infrator": "CONDUTOR", "indicadorRecurso": 0, "placa": "OMK1C21", "placaAtualVeiculo": "OMK1C21", "ufInfracao": "SP", "possuiFoto": false, "situacao": "", "codigoDesdobramento": "0", "codigoRenainf": 7344623242, "renavam": 1265190841, "niVeiculoInfracao": "27428590000104", "niPossuidorVeiculo": "27428590000104", "dataLimiteApresentacaoRecurso": "2024-02-14T00:00:00Z", "timeline": [{"id": 1, "titulo": "Notificação de Autuação", "descricao": "Notificação de Autuação", "data": "06/11/2023", "status": 1}, {"id": 2, "titulo": "Notificação de Penalidade", "descricao": "Notificação de Penalidade", "data": "02/01/2024", "status": 1}, {"id": 3, "titulo": "Registro de pagamento", "descricao": "Registro de pagamento", "data": "", "status": 0}, {"id": 4, "titulo": "Vencimento da infração", "descricao": "Vencimento da infração", "data": "14/02/2024", "status": 1}], "mensagemOrgaoCompetente": "", "mostrarIndicarCondutor": true, "habilitarIndicarCondutor": false, "mensagemIndicarCondutorDesabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais indicar condutor.", "mostrarPedidoBoletoSemDesconto": true, "habilitarPedidoBoletoSemDesconto": true, "habilitarDesconto40": false, "mostrarDesconto40": true, "mensagemDesconto40Desabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais solicitar boleto com desconto para essa infração. Solicite seu boleto pela opção sem desconto.", "habilitarDesconto20": false, "mostrarDesconto20": true, "mensagemDesconto20Desabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais solicitar boleto com desconto para essa infração. Solicite seu boleto pela opção sem desconto.", "mostrarExibirBoleto": true, "habilitarExibirBoleto": false, "mensagemExibirBoletoDesabilitado": "Você não possui boleto para visualizar.\nPeça um boleto para pagamento através de uma das opções disponíveis.", "mostrarMensagemConfirmacao20": true, "permiteBoletoSne": true, "statusOrgaoCompetente": "ATIVA", "descricaoStatusBoletoSne": "SEM_PEDIDO_BOLETO", "statusBoletoSne": 0, "chaveInfracao": "1262001C 05295787455", "reconhecimentoSne": false, "motivoDesconto20Desabilitado": 8, "motivoDesconto40Desabilitado": 8, "motivoPedidoBoletoSemDescontoDesabilitado": -1, "motivoExibirBoletoDesabilitado": 13, "codigoTipoInfratorConsiderado": "TIPO_PROPRIETARIO_A_EPOCA", "mostrarSolicitarDefesaPrevia": false, "habilitarSolicitarDefesaPrevia": false, "mensagemSolicitarDefesaPreviaDesabilitado": "Data limite de defesa prévia expirada. Utilize a opção de solicitar recurso.", "mostrarSolicitarRecurso": false, "habilitarSolicitarRecurso": false, "mensagemSolicitarRecursoDesabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais solicitar recurso para essa infração.", "mostrarReconhecerInfracao": true, "habilitarReconhecerInfracao": false, "mensagemReconhecerInfracaoDesabilitado": "Não é possível reconhecer infração, pois não é possível solicitar boleto com desconto de 40%.", "advertenciaPorEscrito": false, "tipoConsultaInfracao": "COMPLETA", "mostrarPdfNA": true, "habilitarPdfNA": true, "mostrarPdfNP": true, "habilitarPdfNP": true, "mostrarPdfAdvertenciaPorEscrito": true, "habilitarPdfAdvertenciaPorEscrito": false, "mensagemPdfAdvertenciaPorEscritoDesabilitado": "Infração não é penalidade de advertência por escrito.", "mostrarIndicarRealInfrator": true, "habilitarIndicarRealInfrator": false, "mensagemIndicarRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "mostrarRecusarRealInfrator": true, "habilitarRecusarRealInfrator": false, "mensagemRecusarRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "mostrarAceitarRealInfrator": true, "habilitarAceitarRealInfrator": false, "mensagemAceitarRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "mostrarCancelarRealInfrator": true, "habilitarCancelarRealInfrator": false, "mensagemCancelarRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "mostrarDocumentoRealInfrator": true, "habilitarDocumentoRealInfrator": false, "mensagemDocumentoRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "mostrarFuncionalidadesRealInfrator": true, "habilitarFuncionalidadesRealInfrator": false, "mensagemFuncionalidadesRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "dataLimiteIndicacaoRealInfrator": "2023-12-13T00:00:00Z", "existeDocumentoAssinadoRealInfrator": false, "veiculoAutuadoDescricaoMarcaModelo": "MBENZ/MPOLO PARADISO LD"}, {"__boleto__": {"file_id": "06bdb692-2ff9-472c-aed4-742aa64fc630", "linha_digitavel_codigo_barras": "00190.00009 03043.044902 17991.986179 5 96910000007810", "vencimento_do_boleto": "19/04/2024"}, "tipoInfracao": 2, "codigoOrgaoAutuador": "293730", "numeroAutoInfracao": "R024848049", "codigoInfracao": "7455", "descricaoInfracao": "TRANSITAR EM VELOCIDADE SUPERIOR A MAXIMA PERMITIDA EM ATE 20%", "descricaoOrgaoAutuador": "PREF. DE GO GOIANIA", "codigoOrgaoCompetente": "293730", "descricaoOrgaoCompetente": "PREF. DE GO GOIANIA", "dataInfracao": "2023-10-14T05:09:00Z", "localOcorrenciaInfracao": "AV. MARG. BOTAFOGO QD E LT 13-ST L. UNIVERSITARIO B. - SENT. SUL/NORTE", "descricaoMunicipioInfracao": "GOIANIA", "dataNotificacaoAutuacao": "2023-10-22T00:00:00Z", "dataNotificacaoPenalidade": "2023-12-07T00:00:00Z", "dataVencimentoPenalidade": "2024-01-22T00:00:00Z", "dataLimiteDefesaPrevia": "2023-12-06T00:00:00Z", "valorIntegralInfracao": 130.16, "codigoTipoAIT": 2, "urlOrgaoAutuador": "", "indicadorCondutor": 0, "infrator": "CONDUTOR", "indicadorRecurso": 0, "placa": "OMK1C21", "placaAtualVeiculo": "OMK1C21", "ufInfracao": "GO", "possuiFoto": false, "situacao": "", "codigoDesdobramento": "0", "codigoRenainf": 7321119530, "renavam": 1265190841, "niVeiculoInfracao": "27428590000104", "niPossuidorVeiculo": "27428590000104", "dataLimiteApresentacaoRecurso": "2024-01-22T00:00:00Z", "timeline": [{"id": 1, "titulo": "Notificação de Autuação", "descricao": "Notificação de Autuação", "data": "22/10/2023", "status": 1}, {"id": 2, "titulo": "Notificação de Penalidade", "descricao": "Notificação de Penalidade", "data": "07/12/2023", "status": 1}, {"id": 3, "titulo": "Registro de pagamento", "descricao": "Registro de pagamento", "data": "", "status": 0}, {"id": 4, "titulo": "Vencimento da infração", "descricao": "Vencimento da infração", "data": "22/01/2024", "status": 1}], "mensagemOrgaoCompetente": "", "mostrarIndicarCondutor": true, "habilitarIndicarCondutor": false, "mensagemIndicarCondutorDesabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais indicar condutor.", "mostrarPedidoBoletoSemDesconto": true, "habilitarPedidoBoletoSemDesconto": true, "habilitarDesconto40": false, "mostrarDesconto40": true, "mensagemDesconto40Desabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais solicitar boleto com desconto para essa infração. Solicite seu boleto pela opção sem desconto.", "habilitarDesconto20": false, "mostrarDesconto20": true, "mensagemDesconto20Desabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais solicitar boleto com desconto para essa infração. Solicite seu boleto pela opção sem desconto.", "mostrarExibirBoleto": true, "habilitarExibirBoleto": false, "mensagemExibirBoletoDesabilitado": "Você não possui boleto para visualizar.\nPeça um boleto para pagamento através de uma das opções disponíveis.", "mostrarMensagemConfirmacao20": true, "permiteBoletoSne": true, "statusOrgaoCompetente": "ATIVA", "descricaoStatusBoletoSne": "SEM_PEDIDO_BOLETO", "statusBoletoSne": 0, "chaveInfracao": "293730R0248480497455", "reconhecimentoSne": false, "motivoDesconto20Desabilitado": 8, "motivoDesconto40Desabilitado": 8, "motivoPedidoBoletoSemDescontoDesabilitado": -1, "motivoExibirBoletoDesabilitado": 13, "codigoTipoInfratorConsiderado": "TIPO_PROPRIETARIO_A_EPOCA", "mostrarSolicitarDefesaPrevia": false, "habilitarSolicitarDefesaPrevia": false, "mensagemSolicitarDefesaPreviaDesabilitado": "Data limite de defesa prévia expirada. Utilize a opção de solicitar recurso.", "mostrarSolicitarRecurso": false, "habilitarSolicitarRecurso": false, "mensagemSolicitarRecursoDesabilitado": "Essa penalidade já está vencida, por esse motivo você não poderá mais solicitar recurso para essa infração.", "mostrarReconhecerInfracao": true, "habilitarReconhecerInfracao": false, "mensagemReconhecerInfracaoDesabilitado": "Não é possível reconhecer infração, pois não é possível solicitar boleto com desconto de 40%.", "advertenciaPorEscrito": false, "tipoConsultaInfracao": "COMPLETA", "mostrarPdfNA": true, "habilitarPdfNA": true, "mostrarPdfNP": true, "habilitarPdfNP": true, "mostrarPdfAdvertenciaPorEscrito": true, "habilitarPdfAdvertenciaPorEscrito": false, "mensagemPdfAdvertenciaPorEscritoDesabilitado": "Infração não é penalidade de advertência por escrito.", "mostrarIndicarRealInfrator": true, "habilitarIndicarRealInfrator": false, "mensagemIndicarRealInfratorDesabilitado": "Órgão Autuador ainda não realizou adesão à Solução para Indicação de Real Infrator on-line.", "mostrarRecusarRealInfrator": true, "habilitarRecusarRealInfrator": false, "mensagemRecusarRealInfratorDesabilitado": "Órgão Autuador ainda não realizou adesão à Solução para Indicação de Real Infrator on-line.", "mostrarAceitarRealInfrator": true, "habilitarAceitarRealInfrator": false, "mensagemAceitarRealInfratorDesabilitado": "Órgão Autuador ainda não realizou adesão à Solução para Indicação de Real Infrator on-line.", "mostrarCancelarRealInfrator": true, "habilitarCancelarRealInfrator": false, "mensagemCancelarRealInfratorDesabilitado": "Órgão Autuador ainda não realizou adesão à Solução para Indicação de Real Infrator on-line.", "mostrarDocumentoRealInfrator": true, "habilitarDocumentoRealInfrator": false, "mensagemDocumentoRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "mostrarFuncionalidadesRealInfrator": true, "habilitarFuncionalidadesRealInfrator": false, "mensagemFuncionalidadesRealInfratorDesabilitado": "O prazo para indicar real infrator está expirado.", "dataLimiteIndicacaoRealInfrator": "2023-12-06T00:00:00Z", "existeDocumentoAssinadoRealInfrator": false, "veiculoAutuadoDescricaoMarcaModelo": "MBENZ/MPOLO PARADISO LD"}]}}