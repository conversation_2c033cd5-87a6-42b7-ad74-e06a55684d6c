# -*- coding: utf-8 -*-
# origin repository git sha: 596d8a68efa75879f2ecc5a5585fafdcf0f6b866
import json
import os
import re
import sys
import urllib
import urllib.parse
import uuid
from datetime import datetime as dt
from time import sleep

import pdftotext
from scrapy import (
    signals,
    Selector,
    Request
)
from selenium.webdriver.common.by import By

from brobot_bots.external_modules import custom_messages
from brobot_bots.external_modules import pdf
from brobot_bots.external_modules.custom_errors import (InvalidRequestParams,
                                                        PageMalfunction,
                                                        UndefinedError,
                                                        CaptchaIncorrectlySolved,
                                                        XhrError,
                                                        KnownWebsiteBug)
from brobot_bots.external_modules.external_functions import CustomSpider, trap_start_requests
from brobot_bots.items import BrobotBotsItem

path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if path not in sys.path:
    sys.path.insert(1, path)


# fixme This spider is using caching ONLY for API call that is searching for infracoes_de_transito
#  and it is only for screenshot, this is NOT normal caching from s3 like in other spiders,
#  but this caching is done locally from spider itself to selenium for screenshot,
#  when we solve hcaptcha for spider and send request to serpro_radar,
#  we are using that response inside selenium by intercepting request, this way we are not solving another hcaptcha
#  for screenshot and it makes spider works way more reliable when capture_screenshot = True



class autos_serpro_radar_spider(CustomSpider):
    # required scraper name
    name = "autos_serpro_radar"

    # initial urls
    base_url = "https://radar.serpro.gov.br" 

    start_url = "https://radar.serpro.gov.br/main.html#/cidadao"
    search_url = 'https://radar.serpro.gov.br/core-rest/gip-rest/consulta-publica/pesquisar-multas/%s/%s?captcha_response=%s'

    detail_url = 'https://radar.serpro.gov.br/core-rest/gip-rest/consulta-publica/multa/%s?chave=%s'
    first_pdf_urls_np = [
        "https://radar.serpro.gov.br/report/gip-rest/consulta-publica/multa/impressao-np/%s?chave=%s"]
    first_pdf_urls_na = [
        "https://radar.serpro.gov.br/report/gip-rest/consulta-publica/multa/antecipacao-boleto/%s?chave=%s"]

    second_pdf_urls = ["https://radar.serpro.gov.br/report/gip-rest/consulta-publica/multa/impressao-na/%s?chave=%s"]


    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """Rewriting of the spider_idle function to yield result after spider closed."""
        spider = super(autos_serpro_radar_spider, cls).from_crawler(
            crawler, *args, **kwargs
        )
        crawler.signals.connect(spider.get_final_result, signals.spider_idle)
        return spider

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.proxy_required = True

        self.retries = 3
        self.hcaptcha_sitekey = None
        self.hcaptcha_id = None
        # site responses are all in JSON format
        self.validate_empty_keys = False
        self.cached_response = {}

    def validate_splash_response(self, json_response):
        # Load captcha request

        captcha_request = json_response.get("captcha_request")
        if captcha_request:
            # Report incorrect captcha
            self.incorrect_captcha_report("2Captcha", captcha_request)
            raise ValueError("Incorrect captcha solved during screenshot.")

    @trap_start_requests
    def start_requests(self):
        if not self.valid_credentials(['renavam', 'placa'], clean=True):
            return
        
        if self.review_dates:
            error = InvalidRequestParams(message=custom_messages.INVALID_DATE_FORMAT)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        yield Request(
            url=self.start_url,
            callback=self.get_js_path,
            errback=self.errback_func,
            dont_filter=True
        )

    def get_js_path(self, response):
        url = 'https://radar.serpro.gov.br/' + response.selector.xpath("//script[contains(@src,'config')]/@src").get('')
        yield Request(
            url=url,
            callback=self.parse,
            errback=self.errback_func,
            dont_filter=True
        )

    def errback_func_serpro(self, failure):
        if self.retries > 0:
            self.retries -= 1
            yield from self.start_requests()
            return
        self.errback_func(failure)


    def parse(self, response):
        """
        Function to solva captcha and submit search via api
        """

        try:
            self.hcaptcha_sitekey = re.findall(r'hCaptchaKey:"(.*?)"', response.text)[0]
        except:
            if self.retries > 0:
                self.retries -= 1
                yield from self.start_requests()
                return
            error = PageMalfunction(message=custom_messages.PAGE_MALFUNCTION, details=f"Website didn't load captcha. Please, try again.")
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        # Solve captcha
        hcaptcha_txt, self.user_agent = self.solve_hcaptcha_with_smart_api_temporary(option='66')
        self.logger.warning(f'Smartapi returned hcaptcha token: {hcaptcha_txt}')
        if not hcaptcha_txt:
            self.logger.warning(f'Smartapi not returned hcaptcha token. This is self.errors: {self.errors}')
            return

        self.logger.info(
            "Captcha solving result: %s" % hcaptcha_txt
        )

        # Submit query
        yield Request(
            url=self.search_url % (
                self.placa,
                self.renavam,
                hcaptcha_txt,
            ),
            callback=self.parse_search,
            errback=self.errback_func_serpro,
            meta={"handle_httpstatus_list": [422]},
            dont_filter=True
        )


    def parse_search(self, response):
        """
        Function to parse api search result json
        """

        # Check if captcha was correctly solved:

        if response.status == 422:
            if 'Captcha expirado ou inválido' in response.text:
                if self.incorrect_captcha_retries > 0:
                    # we cannot report this captcha as incorrect for brightdata
                    self.incorrect_captcha_retries -= 1
                    yield from self.start_requests()
                else:
                    error_msg = f'Captcha could not be correctly solved and accepted by website after 3 attempts'
                    message = "Erro ao tentar resolver o captcha. Por favor, tente novamente mais tarde. Caso o erro persista, notifique o time de desenvolvimento."
                    error = CaptchaIncorrectlySolved(details=error_msg, message=message)
                    self.errors.append(error.to_dict())
                    self.logger.error(error)
                return
            else:
                self.logger.info(response.text)
                error = UndefinedError(details="Site returned 422 but without incorrect captcha message", message=custom_messages.UNDEFINED_ERROR)
                self.logger.error(error)
                self.errors.append(error.to_dict())
            return

        # Check response status to know if empty result
        if (response.status == 200 and response.text == '') or response.status == 204:
            self.result["sem_infracoes"] = "* Não existem infrações em andamento para a PLACA e RENAVAM informados."
            results_json = []
        else:
            try:
                results_json = json.loads(response.text)
            except Exception as exc:
                error_details = "JSON response is not on the expected state for infracoes. Details:" + str(exc)
                error = XhrError(details=error_details, response=response, message=custom_messages.XHR_ERROR)
                self.errors.append(error.to_dict())
                self.logger.error(error)
                return

        # Screenshot
        if self.capture_screenshot:
            # This is for caching response so we don't need to solve another hcaptcha for screenshot
            # URL of caching is: https://radar.serpro.gov.br/core-rest/gip-rest/consulta-publica/pesquisar-multas/....
            original_url = response.request.url.split('?')[0].replace('https', 'http')
            res_headers = {k.decode(): v[0].decode() for k, v in dict(response.headers).items()}
            self.cached_response[original_url] = [response.body, res_headers]
            self.handle_retry_snapshot_engine(
                self.upload_screenshot_with_snapshot_engine,
                "png",
                start_url=self.start_url,
                rotate_proxy=True,
                max_retries=2
            )

        # Return if no result
        if not results_json:
            return

        # Init array
        self.result["infracoes_de_transito"] = []
        # Parse documents
        for result in results_json:
            # Check if infracao is between start / end date
            infracao_date = dt.strptime(result['dataInfracao'].split()[0], "%d/%m/%Y")
            if not self.start_date <= infracao_date <= self.end_date:
                continue

            yield Request(
                url=self.detail_url % (
                    result.get("id"),
                    urllib.parse.quote(result.get("key"), safe='')
                ),
                callback=self.parse_detail,
                errback=self.errback_func,
                meta={'handle_httpstatus_list': [500]},
                dont_filter=True
            )


    def parse_detail(self, response):
        """
        Function to extract detail search result from second api submit
        """

        # Load result json
        try:
            result_json = json.loads(response.text)
        except Exception as exc:
            error_details = "JSON response is not on the expected state for infracoes details. Details:" + str(exc)
            error = error = XhrError(details=error_details, response=response, message=custom_messages.XHR_ERROR)
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return
        
        if response.status == 500:
            error = KnownWebsiteBug(message="O portal apresentou um problema ao exibir detalhes das multas. Por favor, tente mais tarde.", details=result_json.get('mensagem'))
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        # We don't get 'multas' that have this
        if result_json.get('orgaoDescricao', '') == "AGÊNCIA NACIONAL DE TRANSPORTES TERRESTRES":
            return
        
        # Append to result array
        self.result["infracoes_de_transito"].append(result_json)

        # histAcoes are used to check what file is available to download.
        hist_acoes = list(set(result_json['histAcoes']))
        try:
            hist_acoes.remove(1)
        except ValueError:
            pass
        # Check for pdf file and select correct URL for 'boleto'
        file_stuffs = {
            "boleto": self.first_pdf_urls_np if 10 in hist_acoes else self.first_pdf_urls_na,
            "autuacao": self.second_pdf_urls
        }

        for file_type, url_templates in file_stuffs.items():
            # From my testing we can disregard 1 in hist_acoes, then if there is 3 in hist_acoes we can download autuacao
            # and if there is anything inside except 1 (3, 10, 53....) we can download boleto
            if file_type == 'boleto':
                if not self.get_boletos:
                    continue
                if not hist_acoes:
                    continue

            if file_type == 'autuacao':
                if not self.get_other_files or 3 not in hist_acoes:
                    continue

            for url_template in url_templates:
                yield Request(
                    url=url_template % (
                        result_json.get("id"),
                        urllib.parse.quote(result_json.get("key"), safe='')
                    ),
                    callback=self.parse_pdf,
                    errback=self.errback_func,
                    dont_filter=True,
                    meta={
                        "file_type": file_type,
                        "id": result_json.get("id"),
                        "key": result_json.get("key"),
                        "handle_httpstatus_list": [422, 500]
                    }
                )


    def parse_pdf(self, response):
        """
        Function to download PDF with file type and upload to S3 bucket 
        and call the webhook then.
        """

        # SERPRO now has two urls for boleto pdfs and there is no way to find out which one is correct until testing.
        if response.status == 422:
            self.logger.info(f"url: {response.url} returned 422")
            url = response.request.url.replace(
                'radar.serpro.gov.br/core-rest/gip-rest/consulta-publica/multa/impressao-np',
                'radar.serpro.gov.br/core-rest/gip-rest/consulta-publica/multa/antecipacao-boleto')
            yield Request(
                url,
                callback=self.parse_pdf,
                errback=self.errback_func,
                meta={
                    "file_type": response.meta['file_type'],
                    "id": response.meta["id"],
                    "key": response.meta["key"],
                },
                dont_filter=True)
            return
        file_available = False
        # Load meta
        file_type = response.meta.get("file_type")
        item_id = response.meta.get("id")
        item_key = response.meta.get("key")
        # If response_status != 200 then it is file_unavailable
        if response.status == 200:
            file_available = True
            # Create file name and id
            file_id = str(uuid.uuid4())
            file_name = f'{file_id}.pdf'

            # Save PDF file to folder
            file_path = os.path.join(
                path,
                "downloads",
                self.scrape_id,
                file_name
            )
            with open(file_path, 'wb') as f:
                f.write(response.body)
            self.upload_file(file_id)

        # Save to result json
        for item in self.result.get("infracoes_de_transito"):
            
            # Ignore not matching
            if item.get("id") != item_id or item.get("key") != item_key:
                continue

            if not file_available:
                item[f"__{file_type}__"] = {
                    'file_unavailable': "Órgão consultado não permitiu download do arquivo neste momento."
                }
                break

            # set dictionary for file
            item[f"__{file_type}__"] = {}
            
            # Upload pdf to s3 and call the webhook

            item[f"__{file_type}__"].update({"file_id": file_id})

            if file_type == 'autuacao':
                data_limite, prefeitura = self._extract_data_limite(file_path)
                parsed_pdf = {'data_limite_identificacao_do_condutor': data_limite}

                if prefeitura != 'PREFEITURA DE SÃO JOSÉ DOS CAMPOS' or data_limite:  # ignore validation for this case
                    pdf_fields = {'data_limite_identificacao_do_condutor': pdf.validation.FieldType.DATA}
                    if not self.is_valid_pdf(parsed_pdf, pdf_fields):
                        self.logger.error('Error on validation autuação parsed data: %s', str(file_id))
                        return

                item['__autuacao__'].update(parsed_pdf)
            
            if file_type == 'boleto':
                parsed_pdf = self._parse_pdf(file_path)
                pdf_fields = {
                    'vencimento_do_boleto': pdf.validation.FieldType.DATA,
                    'linha_digitavel_codigo_barras': pdf.validation.FieldType.CODIGO_BARRAS
                }
                if not self.is_valid_pdf(parsed_pdf, pdf_fields):
                    self.logger.info(str(file_id))
                    return
                
                item['__boleto__'].update(parsed_pdf)

            break

    def _extract_data_limite(self, file_path):
        """Extracts the data limite from the PDF file.

        Args:
            file_path (str): Path to the PDF file.

        Returns:
            tuple: A tuple containing the data limite and prefeitura name (for the treatment of a specific case).
        """

        with open(file_path, 'rb') as f:
            pdf = pdftotext.PDF(f)
        try:
            data_limite = re.findall(r'identificação do condutor infrator[: ]+(\d+/\d+/\d+)', pdf[0])
            if not data_limite:
                data_limite = re.findall(r'identificação do Real Infrator[: ]+(\d+/\d+/\d+)', pdf[0])
            data_limite = data_limite[0]
            return data_limite, ''
        except:
            data_limite = ''
            return data_limite, pref if (pref := "PREFEITURA DE SÃO JOSÉ DOS CAMPOS") in pdf[0] else ''


    def _parse_pdf(self, file_path):
        extracted = {}
        with open(file_path, 'rb') as f:
            pdf_text = pdftotext.PDF(f)[-1] # Get the last page
 
        rows = pdf_text.split('\n')
        for index, row in enumerate(rows):
            linha_digitavel = [i.strip() for i in re.findall(r'([\d\. -]{30,})+', row, re.DOTALL) if
                               len(i.strip().replace(' ', '')) > 30]
            if linha_digitavel:
                extracted['linha_digitavel_codigo_barras'] = linha_digitavel[0].strip()
            if 'vencimento' in row.lower() and re.findall(r'\d{2}/\d{2}/\d{4}', rows[index + 1]):
                extracted['vencimento_do_boleto'] = re.findall(r'\d{2}/\d{2}/\d{4}', rows[index + 1])[0]

        return extracted

    def validate_snapshot_response(self):

        if self.snapshot_engine.find_elements(By.XPATH, "//p[@class='text-danger']//span[contains(text(),'Captcha expirado')]"):
            # We need to raise error so it will be logged in errors,
            # in this case we are not checking by usual way:
            # (if not self.report_incorrect_captcha(self.hcaptcha_id)...)
            # as allow_retry for taking screenshot is taking precedence
            # update_error = True is because we don't want to log error if captcha was wrong
            self.report_incorrect_captcha(self.hcaptcha_id, update_error=False)
            raise ValueError("incorrect hcaptcha")

        if not self.snapshot_engine.find_elements(By.XPATH, "//div[@class='row'][not(contains(category,'ng-hide'))]//fieldset[@class='info-veiculo']") \
            and not self.snapshot_engine.find_elements(By.XPATH, "//p[@class='text-danger']//span[contains(text(),'existem infrações')]"):
            raise ValueError("Page isn't loaded")

    def execute_render_with_selenium_script(self, **kwargs):

        self.execute_wait_for_snapshot_engine(css=".row>button[type=submit]")
        # wait for hcaptcha iframe or we will create our own iframe it it is not shown
        try:
            self.execute_wait_for_snapshot_engine(xpath="//iframe[@src]", delay=20)
        except ValueError:
            pass

        # Send renavam
        input_renavam = self.snapshot_engine.find_element(By.CSS_SELECTOR, "#idRenavam")
        input_renavam.click()
        input_renavam.send_keys(self.renavam)

        # Send placa
        input_placa = self.snapshot_engine.find_element(By.CSS_SELECTOR, "#idPlaca")
        input_placa.click()
        input_placa.send_keys(self.placa)

        # For some reason, when there are no 'infracoes', the alert message sometimes doesn't show up in the Selenium screenshot.  
        # To handle this, we insert the alert message.  
        if sem_infracoes_msg := self.result.get('sem_infracoes'):
            self.snapshot_engine.execute_script(f"""
                let form = document.querySelector('form[name="ConsultaPublicaCtrl.formDados"]');
                let br = form?.nextElementSibling?.tagName === 'BR' ? form.nextElementSibling : null;

                let newDiv = document.createElement('div');
                newDiv.className = "alert alert-warning ng-scope";
                newDiv.setAttribute("ng-if", "!ConsultaPublicaCtrl.multa && ConsultaPublicaCtrl.pesquisaRealizada");
                newDiv.innerHTML = `
                    <p class="text-danger">
                        <span ng-show="!ConsultaPublicaCtrl.multa && ConsultaPublicaCtrl.pesquisaRealizada" class="ng-binding">
                            {sem_infracoes_msg}
                        </span>
                    </p>`;
                br.after(newDiv);
            """)

            sleep(2)
            return

        # Solve captcha
        selector = Selector(text=self.snapshot_engine.page_source)
        hcaptcha_src = selector.xpath('//iframe[@src]/@src').get('')
        #self.snapshot_engine.save_screenshot('teste.png')

        # Solve captcha
        # WARNING: for now we are not solving this hcaptcha, because we are using cache response from scrapy
        #  search for cached_response in this spider and see _fixme at start of spider
        self.hcaptcha_id, hcaptcha_txt = None, 'null'
        '''
        try:
            self.hcaptcha_id, hcaptcha_txt = \
                self.solve_captcha("HCAPTCHA", sitekey=self.hcaptcha_sitekey, captcha_url=self.start_url)
        except Error as e:
            return
        '''

        if not hcaptcha_src:
            script = '''
            const node = document.createElement("iframe");
            document.querySelector("#idHcaptcha-cidadao").appendChild(node);
            '''
            self.snapshot_engine.execute_script(script)

        self.snapshot_engine.execute_script(f'document.querySelector("iframe").setAttribute("data-hcaptcha-response", "{hcaptcha_txt}")')

        sleep(1)
        submit_button = '.row>button[type="submit"]'
        self.snapshot_engine.execute_script(f'document.querySelector(\'{submit_button}\').click()')

        self.execute_wait_for_snapshot_engine('div[class="loading-root ng-hide"]', reverse=True)

        selector = Selector(text=self.snapshot_engine.page_source)
        if selector.xpath('//div[@class="btn-agree"]'):
            agree_button = self.snapshot_engine.find_element(By.XPATH, '//div[@class="btn-agree"]/button')
            agree_button.click()

    def snapshot_request_interceptor(self, request):
        original_url = request.url.split('?')[0].replace('https', 'http')

        # We are only using caching for API call that is doing search for infracoes_de_transito
        if self.cached_response.get(original_url) and isinstance(self.cached_response.get(original_url)[0], bytes):
            content, headers = self.cached_response.get(original_url)
            request.create_response(
                status_code=200,
                headers=headers,
                body=content
            )


    def get_final_result(self, spider):
        """Will be called before spider closed
        Used to save data_collected result."""

        # stop crawling after yeild_item called
        if not self.result_received:
            # push to webhook
            if self.screenshots_ids:
                self.result['__screenshots_ids__'] = self.screenshots_ids
            self.data = {
                'scrape_id': self.scrape_id,
                'scraper_name': self.name,
                'files_count': self.files_count,
                'screenshots_count': self.screenshots_count,
                'cnpj': self.cnpj}
            self.data.update({'result': self.result})
            if self.errors:
                self.data.update({'errors': self.unique_list(self.errors)})
            webhook_file_path = os.path.join(
                path, "downloads", self.scrape_id,
                '{renavam}-data_collected.json'.format(renavam=self.renavam))
            self.data_collected(self.data, webhook_file_path)

            # return item for scrapinghub
            self.result_received = True
            req = Request("http://example.com", callback=self.yield_item,
                          errback=self.yield_item, dont_filter=True)
            self.crawler.engine.crawl(req, spider)


    def yield_item(self, response):
        """Function is using to yield Scrapy Item
        Required for us to see the result in ScrapingHub"""
        item = BrobotBotsItem()
        item.update(self.data)
