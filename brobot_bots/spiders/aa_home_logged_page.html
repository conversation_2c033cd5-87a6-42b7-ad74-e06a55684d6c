<!DOCTYPE html>
<html>
<head>
    <link rel="shortcut icon" href="/detranextratos/favicon.ico" type="image/x-icon"/>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Detran-PR </title>
    <link href="/detranextratos/v2/css/bootstrap.min.css" rel="stylesheet" id="bootstrap-css">
    <link href="/detranextratos/v2/css/font-awesome.min.css" rel="stylesheet">
    <link href="/detranextratos/v2/css/toastr.css" rel="stylesheet">

    <link href="/detranextratos/v2/css/extrato.css?v=V04_03_73_pro" rel="stylesheet">
    <link href="/detranextratos/v2/css/bootstrap.vertical-tabs.min.css" rel="stylesheet">

    <script src="/detranextratos/v2/js/jquery-1.12.2.min.js"></script>
    <script src="/detranextratos/v2/js/bootstrap.min.js"></script>

    <script src="/detranextratos/v2/js/jquery.fileDownload.js"></script>

    <script src="/detranextratos/v2/js/cookie.js"></script>
    <script src="/detranextratos/v2/js/toastr.js"></script>


    <script type="text/javascript"
            src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.3/jquery-ui.min.js"></script>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-42524529-6"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'UA-42524529-6');
    </script>

</head>
<body>
<div class="loader-box" id='loader'>
    <br>
    <div class="loader">
        <div class="element-animation">
            <img src="images/loader.png" width="1180" height="70" ;>
        </div>
    </div>
    <br>
    <div class="lable">
        Carregando
    </div>
</div>
<div class="container" id='container'>


    <script src="/detranextratos/v2/js/extrato.js?v=V04_03_73_pro"></script>


    <script src="/detranextratos/js/validacao/verificador.js"></script>
    <div style="padding-top: 10px;"></div>
    <style>
        #msg {
            background-color: #EEEEEE;
            border: 1px solid #CCCCCC;
            padding: 7px;
            font-family: Verdana, Arial, Helvetica, sans-serif;
            font-size: 10px;
            color: #00004C;
            text-align: center;
            margin-bottom: 10px;
        }

        .msg_erro {
            color: #CC0000;
        }

        .msg_aviso {
            color: #000000;
            /*width: 500px;*/
            vertical-align: middle;
        }

        .msg_sucesso {
            color: #006600;
        }

        .msg_descricao {
            color: #999999;
            font-style: italic;
        }
    </style>

    <div id="__msgVerificador"></div>

    <header>
        <div class="logo">
            <img src="/detranextratos/images/logo-detran.png" width="152" height="43"/>
        </div>
        <h1>Consulta Consolidada do Veículo</h1>
        <meta charset="ISO-8859-1">
    </header>


    <section id="main">
        <section class="main-area">
            <div class="content">


                <div class="form-group col-xs-8">
                    <label for="renavam">Nome:</label>
                    <div>DAVI GUSTAVO PAROLIN</div>
                </div>
                <div class="form-group col-xs-3">
                    <label for="renavam">CPF:</label>
                    <div>22379478805</div>
                </div>
                <div class="form-group col-xs-1">
                    <a href="/detranextratos/geraExtratoAutenticado.do?action=signout" class="btn btn-danger"><i
                            class="fa fa-sign-out"></i> Sair</a>
                </div>


            </div>


            <script src="/detranextratos/v2/js/jquery.validate.js"></script>
            <script src="/detranextratos/v2/js/jquery.mask.min.js"></script>
            <script src="/detranextratos/v2/js/extrato.js?v=V04_03_73_pro"></script>
            <link href="/detranextratos/v2/css/music.css?v=1749565978458" rel="stylesheet">

            <div class="music"></div>

            <div id="loginbox"
                 class="mainbox col-lg-6 col-lg-offset-3 col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2  col-xs-12">
                <div class="panel panel-default" style="background-color: white">
                    <div style="padding-top:30px" class="panel-body">
                        <form id="loginform" class="form-horizontal" role="form">
                            <div class="input-group center">
						<span class="input-group-addon" style="min-width: 37px; padding: 0">
							<i class="fa fa-car"></i>
						</span>
                                <div class="groupPesq">
                                    <input id="login-renavam" autocomplete="off" type="text"
                                           class="form-control renavam" name="renavam" value="" placeholder="Renavam"
                                           maxlength="13">
                                </div>

                            </div>


                            <script src='https://www.wsutils.detran.pr.gov.br/detran-wsutils/common/js/captcha.js'></script>
                            <div class='input-group center' style='margin-top: 4px'><span class='input-group-addon'
                                                                                          style='min-width: 37px; padding: 0'>		<i
                                    class='fa fa-unlock-alt'></i>	  </span>
                                <div class=' text-center' style='height: auto; width: 100%; padding:0px'>
                                    <div class='form-control' style='height: auto; min-height: 50px' align='center;'>
                                        <img id='imagemCaptcha' title='Imagem do Captcha'
                                             src='data:image/png;base64,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'/>
                                    </div>
                                </div>
                                <div class='form-control' style='height: auto;'><input type='text' autocomplete='off'
                                                                                       id='id_senha' name='senha'
                                                                                       class='form-control'
                                                                                       maxlength='6' size='9'
                                                                                       oninput='javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);'
                                                                                       title='Digite o texto da imagem'
                                                                                       placeholder='Imagem de Controle'/>
                                </div>
                                <span class='input-group-addon btn-group-vertical' role='group' aria-label='...'
                                      style='width: 37px'>		  <a class='btn btn-info btn-xs btn-captcha'
                                                                       title='Atualizar a imagem'
                                                                       style='white-space: normal; padding: 0px;'
                                                                       id='btnReload' href='javascript:void(0);'>		     <span
                                        class='glyphicon glyphicon-refresh'></span>		  </a>	    </span></div>
                            <script>
                                $('#btnReload').click(function(e){		$('#id_senha').val('');    __refreshCaptcha();});$(function(){    setInterval(function(){     __refreshCaptcha();    }, (540000));});function __refreshCaptcha(){		$.ajax({			url:'rest/captcha/req',			type:'GET', dataType:'text',			success: function(data, status, xhr){    			$('#imagemCaptcha').attr('src', data);			},			error: function(data, status, xhr){$('#imagemCaptcha').attr('src', ''); }		});}
                            </script>

                            <div style="margin-top:30px" class="form-group ">
                                <div class="col-sm-12 controls text-center">
                                    <a id="btn-login" href="javascript:void(0);" class="btn btn-primary">Pesquisar </a>
                                    <a id="btn-fblogin" href="javascript:void(0);" class="btn btn-primary">Limpar</a>
                                </div>
                            </div>
                            <input type="hidden" name="q" value="q"/>
                            <input type="hidden" name="fmr" value="58062025103211"/>
                        </form>
                        <div style="text-decoration:none; text-align: center; width:100%; color:#CCCCCC; font-size:10px;">
                            (vers&atilde;o:V04_03_73_pro de 05/02/2025 às 16:00)
                        </div>
                    </div>
                </div>
            </div>
            <div class="clearfix"></div>
            <div id="messages">
                <div class="col-md-4 col-md-offset-4 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1">


                </div>
            </div>
            <div class="placeholder" id="audioplaceholder"></div>
            <div id="codecf" style="position: absolute; width: 1px; height: 1px; visible: hidden;"></div>
            <script type="text/javascript">
                $.validator.setDefaults({
                    highlight: function(element){
                        $(element).closest('.input-group').not('.ignore').addClass('has-error');
                    },
                    unhighlight: function(element){
                        $(element).closest('.input-group').not('.ignore').removeClass('has-error');
                    },
                    errorElement: 'span',
                    errprClass: 'help-block',
                    errorPlacement: function(error, element){
                        if(element.parent('.input-group').length){
                            error.insertAfter(element.parent());
                        } else {
                            error.insertAfter(element);
                        }
                    }
                });




                    var validator = $("#loginform").validate({
                        rules: {
                            renavam: "required",
                            senha: "required",
                        },
                        messages: {
                            renavam: "",
                            senha: ""
                        }
                    });



                    $('#btn-fblogin').click(function(e){
                        $('.renavam').val('');
                        $('#id_senha').val('');
                        __refreshCaptcha();
                        $('#login-renavam').focus();

                    });

                    $('#btn-login').click(function(e){
                        e.preventDefault();


                        $('#messages').html('');
                        if($("#loginform").valid()) {
                            $("#container").fadeOut({
                                duration:250,
                                complete: function(){
                                    $("#loader").fadeIn({duration:250});
                                    $.ajax({
                                        url:'/detranextratos/geraExtratoAutenticado.do?action=viewExtract',
                                        type:'POST',
                                        data:$("#loginform").serialize(),
                                        dataType:'html',
                                        success: function(data, status, xhr){
                                            $("#loader").fadeOut({
                                                duration:250,
                                                complete: function(){
                                                    gtag('event', 'ajax', {'event_label':'Consulta Extratos', 'event_category':'Consulta'});
                                                    $("#container").html(data);
                                                    $("#container").fadeIn({duration:250});
                                                }
                                            });
                                        },
                                        error: function(data, status, xhr){
                                            $("#loader").fadeOut({duration:250, complete: function(){
                                                __refreshCaptcha();
                                                $('#id_senha').val('');
                                                $("#container").fadeIn({duration:250});
                                                $('#messages').html('<div class="col-md-4 col-md-offset-4 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1"><div class="alert alert-warning col-sm-12" role="alert"><span class="glyphicon glyphicon-warning-sign" aria-hidden="true"></span><span class="sr-only">Error:</span>&nbsp;&nbsp;'+data.responseText+'</div></div>');
                                                $('#messages').fadeIn(300);
                                                $("#container").show();
                                                  $('#login-renavam').focus();
                                                setTimeout(function(){
                                                    $('#messages').fadeOut(300);
                                                    }, 10000
                                                );
                                            }});
                                        }
                                    });
                                }
                            });
                        } else {
                            validator.focusInvalid();
                        }
                    });
                    $(function(){
                        $('#login-renavam').focus();
                        document.title = 'Detran-PR';
                        $('.renavam').mask('00000000000', {reverse: true});
                        disconnect();
                    });
            </script>
        </section>
    </section>


</div>
<span id="statsWS"
      style="position: fixed; top: 100;position: fixed;width: 50%;height: 25px; bottom: 0px;left: 2px;z-index: 1000;"><span
        class="fa fa-circle text-mutted"></span><div></div></span>
</body>

<script>
    toastr.options = {
      "closeButton": false,
      "debug": false,
      "newestOnTop": false,
      "progressBar": false,
      "positionClass": "toast-top-right",
      "preventDuplicates": true,
      "showDuration": "300",
      "hideDuration": "1000",
      "timeOut": "3000",
      "extendedTimeOut": "1000",
      "showEasing": "swing",
      "hideEasing": "linear",
      "showMethod": "fadeIn",
      "hideMethod": "fadeOut"
    }

    $(function(){

        $.ajaxSetup({
            error: function(jqx, exception){
                if(jqx == 400 || jqx == 401){
                    $("#loader").fadeOut({duration:250, complete: function(){
                        $("#container").fadeIn({duration:250});
                        $('#messages').html('<div class="col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2"><p class="danger">'+exception.responseText+'</p></div>');
                        $('#messages').fadeIn(300);
                        setTimeout(function(){
                            $('#messages').fadeOut(300);
                            }, 10000
                        );
                    }});
                }
            }
        });

        setTimeout(function(){
            $('#messages').fadeOut(300);
            }, 10000
        );
    });


    var websocket = null;
    var codRen = null;
    function connect(u, renavam) {
        if(websocket !== null)
            return;
        codRen = renavam;
        disconnect();
        var wsProtocol = window.location.protocol == "https:" ? "wss" : "ws";
        var wsURI = wsProtocol + '://' + window.location.host + '/detranextratos/websocket/'+u+'/'+renavam;
        console.log(wsURI);
        websocket = new WebSocket(wsURI);

        websocket.onopen = function() {
            console.log("conected");
            displayStatus('Open');
        };
        websocket.onmessage = function(event) {
            if(event.data === 'payment'){
                $('.modal').modal('hide');
                $('#myModal').modal('hide');
                $('.modal-backdrop').remove();


                toastr.success("Pagamento Recebido!", "Pagamento", {"positionClass": "toast-top-center", "timeOut": "8000"});
                $('#abas').html('<div class="loader-box" id="loader"><div class="loader"><div class="element-animation"><img src="images/loader.png" width="1180" height="70";></div></div></div>');
                $('#abas .loader-box').fadeIn({
                    duration:250,
                    complete: function(){
                        $.ajax({



                                    url:'/detranextratos/geraExtratoAutenticado.do?action=parteDasAbas&renavam='+codRen,


                            type:'GET',
                            dataType:'html',
                            success: function(data, status, xhr){
                                $("#container").html(data);
                            },
                            error: function(data, status, xhr){
                                $("#container").html(data);
                            }
                        });
                    }
                });
            } else {
                toastr.warning(event.data, "Mensagem", {"positionClass": "toast-top-center", "timeOut": "10000"});
            }
        };
        websocket.onerror = function(event) {
            console.log('Error WS ' + event.data);
        };
        websocket.onclose = function() {
            codRen = null;
            console.log('closed');
            displayStatus('Closed');
        };
    }

    function disconnect() {
        if (websocket !== null) {
            websocket.close();
            websocket = null;
        }
        displayStatus('Closed');
    }

    function displayStatus(status) {
        var currentStatus = document.getElementById('statsWS');
        if(currentStatus!==null){
            if(status === 'Open'){
                currentStatus.innerHTML = "<span class='fa fa-circle text-conn'></span>";
            } else {
                currentStatus.innerHTML = "<span class='fa fa-circle text-mutted'></span>";
            }
        }
    }

</script>

</html>
