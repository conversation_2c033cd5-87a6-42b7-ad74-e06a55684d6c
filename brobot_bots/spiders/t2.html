<html lang="pt-br" class="fontawesome-i2svg-active fontawesome-i2svg-complete"><head>
    
    <!-- Meta tags Obrigatórias -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">svg:not(:root).svg-inline--fa{overflow:visible}.svg-inline--fa{display:inline-block;font-size:inherit;height:1em;overflow:visible;vertical-align:-.125em}.svg-inline--fa.fa-lg{vertical-align:-.225em}.svg-inline--fa.fa-w-1{width:.0625em}.svg-inline--fa.fa-w-2{width:.125em}.svg-inline--fa.fa-w-3{width:.1875em}.svg-inline--fa.fa-w-4{width:.25em}.svg-inline--fa.fa-w-5{width:.3125em}.svg-inline--fa.fa-w-6{width:.375em}.svg-inline--fa.fa-w-7{width:.4375em}.svg-inline--fa.fa-w-8{width:.5em}.svg-inline--fa.fa-w-9{width:.5625em}.svg-inline--fa.fa-w-10{width:.625em}.svg-inline--fa.fa-w-11{width:.6875em}.svg-inline--fa.fa-w-12{width:.75em}.svg-inline--fa.fa-w-13{width:.8125em}.svg-inline--fa.fa-w-14{width:.875em}.svg-inline--fa.fa-w-15{width:.9375em}.svg-inline--fa.fa-w-16{width:1em}.svg-inline--fa.fa-w-17{width:1.0625em}.svg-inline--fa.fa-w-18{width:1.125em}.svg-inline--fa.fa-w-19{width:1.1875em}.svg-inline--fa.fa-w-20{width:1.25em}.svg-inline--fa.fa-pull-left{margin-right:.3em;width:auto}.svg-inline--fa.fa-pull-right{margin-left:.3em;width:auto}.svg-inline--fa.fa-border{height:1.5em}.svg-inline--fa.fa-li{width:2em}.svg-inline--fa.fa-fw{width:1.25em}.fa-layers svg.svg-inline--fa{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.fa-layers{display:inline-block;height:1em;position:relative;text-align:center;vertical-align:-.125em;width:1em}.fa-layers svg.svg-inline--fa{-webkit-transform-origin:center center;transform-origin:center center}.fa-layers-counter,.fa-layers-text{display:inline-block;position:absolute;text-align:center}.fa-layers-text{left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);-webkit-transform-origin:center center;transform-origin:center center}.fa-layers-counter{background-color:#ff253a;border-radius:1em;-webkit-box-sizing:border-box;box-sizing:border-box;color:#fff;height:1.5em;line-height:1;max-width:5em;min-width:1.5em;overflow:hidden;padding:.25em;right:0;text-overflow:ellipsis;top:0;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:top right;transform-origin:top right}.fa-layers-bottom-right{bottom:0;right:0;top:auto;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:bottom right;transform-origin:bottom right}.fa-layers-bottom-left{bottom:0;left:0;right:auto;top:auto;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:bottom left;transform-origin:bottom left}.fa-layers-top-right{right:0;top:0;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:top right;transform-origin:top right}.fa-layers-top-left{left:0;right:auto;top:0;-webkit-transform:scale(.25);transform:scale(.25);-webkit-transform-origin:top left;transform-origin:top left}.fa-lg{font-size:1.3333333333em;line-height:.75em;vertical-align:-.0667em}.fa-xs{font-size:.75em}.fa-sm{font-size:.875em}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-fw{text-align:center;width:1.25em}.fa-ul{list-style-type:none;margin-left:2.5em;padding-left:0}.fa-ul>li{position:relative}.fa-li{left:-2em;position:absolute;text-align:center;width:2em;line-height:inherit}.fa-border{border:solid .08em #eee;border-radius:.1em;padding:.2em .25em .15em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left,.fab.fa-pull-left,.fal.fa-pull-left,.far.fa-pull-left,.fas.fa-pull-left{margin-right:.3em}.fa.fa-pull-right,.fab.fa-pull-right,.fal.fa-pull-right,.far.fa-pull-right,.fas.fa-pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.fa-rotate-90{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-webkit-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-webkit-transform:scale(-1,1);transform:scale(-1,1)}.fa-flip-vertical{-webkit-transform:scale(1,-1);transform:scale(1,-1)}.fa-flip-both,.fa-flip-horizontal.fa-flip-vertical{-webkit-transform:scale(-1,-1);transform:scale(-1,-1)}:root .fa-flip-both,:root .fa-flip-horizontal,:root .fa-flip-vertical,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-rotate-90{-webkit-filter:none;filter:none}.fa-stack{display:inline-block;height:2em;position:relative;width:2.5em}.fa-stack-1x,.fa-stack-2x{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.svg-inline--fa.fa-stack-1x{height:1em;width:1.25em}.svg-inline--fa.fa-stack-2x{height:2em;width:2.5em}.fa-inverse{color:#fff}.sr-only{border:0;clip:rect(0,0,0,0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}.sr-only-focusable:active,.sr-only-focusable:focus{clip:auto;height:auto;margin:0;overflow:visible;position:static;width:auto}</style><link href="/layout_portal/img/favicon.svg" type="image/x-icon" rel="icon"><link href="/layout_portal/img/favicon.svg" type="image/x-icon" rel="shortcut icon">
    
    <title>TRÂNSITO-MG | Consulta de situação do veículo</title>

    <style>
            .nav-image {
            background: url(/publico/img/header/header.jpg) !important;
        }
        :root {
        --servico-default-cor-1: #f5bc37;
        --servico-default-cor-2: #c48e10;
        --servico-default-cor-3: #0b071e;
        --servico-default-cor-4: white;
        /*
        --servico-default-cor-1: ;
        --servico-default-cor-2: ;
        --servico-default-cor-3: ;
        --servico-default-cor-4: ;*/
    }
</style>
    
	<link rel="stylesheet" href="/layout_portal/css/bootstrap.min.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/yamm.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/fontawesome-free.min.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/menu.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/style.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/icones-detran.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/cores-servicos.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/gst/css/jquery-loading.css?1748887530" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/owl.carousel.min.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/layout_portal/css/owl.theme.default.min.css?1748887528" timestamp="force">
	<link rel="stylesheet" href="/css/icon-portal.css?1748887530" timestamp="force">
	<link rel="stylesheet" href="/css/icon-portal-style.css?1748887530" timestamp="force">
	<link rel="stylesheet" href="/css/sistema.sdv8.css?1748887530" timestamp="force">

<link rel="stylesheet" href="/layout_portal/css/print.css?1748887528" media="print" timestamp="force">

	<script src="/layout_portal/js/jquery-3.4.1.min.js?1748887528" timestamp="force"></script>
	<script src="/layout_portal/js/bootstrap.bundle.min.js?1748887528" timestamp="force"></script>
	<script src="/layout_portal/js/fa-icon-all.js?1748887528" timestamp="force"></script>
	<script src="/layout_portal/js/meiomask.min.js?1748887528" timestamp="force"></script>
	<script src="/layout_portal/js/sistema.js?1748887528" timestamp="force"></script>
	<script src="/layout_portal/js/acessibilidade.js?1748887528" timestamp="force"></script>
	<script src="/layout_portal/js/owl.carousel.min.js?1748887528" timestamp="force"></script>
	<script src="/gst/js/jquery-loading.js?1748887530" timestamp="force"></script>
	<script src="/gst/js/sweetalert2.all.min.js?1748887530" timestamp="force"></script><style>@charset "UTF-8";.swal2-popup.swal2-toast{flex-direction:row;align-items:center;width:auto;padding:.625em;overflow-y:hidden;box-shadow:0 0 .625em #d9d9d9}.swal2-popup.swal2-toast .swal2-header{flex-direction:row}.swal2-popup.swal2-toast .swal2-title{flex-grow:1;justify-content:flex-start;margin:0 .6em;font-size:1em}.swal2-popup.swal2-toast .swal2-footer{margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-popup.swal2-toast .swal2-close{position:static;width:.8em;height:.8em;line-height:.8}.swal2-popup.swal2-toast .swal2-content{justify-content:flex-start;font-size:1em}.swal2-popup.swal2-toast .swal2-icon{width:2em;min-width:2em;height:2em;margin:0}.swal2-popup.swal2-toast .swal2-icon::before{display:flex;align-items:center;font-size:2em;font-weight:700}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-popup.swal2-toast .swal2-icon::before{font-size:.25em}}.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-popup.swal2-toast .swal2-actions{flex-basis:auto!important;width:auto;height:auto;margin:0 .3125em}.swal2-popup.swal2-toast .swal2-styled{margin:0 .3125em;padding:.3125em .625em;font-size:1em}.swal2-popup.swal2-toast .swal2-styled:focus{box-shadow:0 0 0 .0625em #fff,0 0 0 .125em rgba(50,100,150,.4)}.swal2-popup.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;transform:rotate(45deg);border-radius:50%}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.8em;left:-.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-popup.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-popup.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-popup.swal2-toast.swal2-show{-webkit-animation:swal2-toast-show .5s;animation:swal2-toast-show .5s}.swal2-popup.swal2-toast.swal2-hide{-webkit-animation:swal2-toast-hide .1s forwards;animation:swal2-toast-hide .1s forwards}.swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-tip{-webkit-animation:swal2-toast-animate-success-line-tip .75s;animation:swal2-toast-animate-success-line-tip .75s}.swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-long{-webkit-animation:swal2-toast-animate-success-line-long .75s;animation:swal2-toast-animate-success-line-long .75s}.swal2-container{display:flex;position:fixed;z-index:1060;top:0;right:0;bottom:0;left:0;flex-direction:row;align-items:center;justify-content:center;padding:.625em;overflow-x:hidden;transition:background-color .1s;background-color:transparent;-webkit-overflow-scrolling:touch}.swal2-container.swal2-top{align-items:flex-start}.swal2-container.swal2-top-left,.swal2-container.swal2-top-start{align-items:flex-start;justify-content:flex-start}.swal2-container.swal2-top-end,.swal2-container.swal2-top-right{align-items:flex-start;justify-content:flex-end}.swal2-container.swal2-center{align-items:center}.swal2-container.swal2-center-left,.swal2-container.swal2-center-start{align-items:center;justify-content:flex-start}.swal2-container.swal2-center-end,.swal2-container.swal2-center-right{align-items:center;justify-content:flex-end}.swal2-container.swal2-bottom{align-items:flex-end}.swal2-container.swal2-bottom-left,.swal2-container.swal2-bottom-start{align-items:flex-end;justify-content:flex-start}.swal2-container.swal2-bottom-end,.swal2-container.swal2-bottom-right{align-items:flex-end;justify-content:flex-end}.swal2-container.swal2-bottom-end>:first-child,.swal2-container.swal2-bottom-left>:first-child,.swal2-container.swal2-bottom-right>:first-child,.swal2-container.swal2-bottom-start>:first-child,.swal2-container.swal2-bottom>:first-child{margin-top:auto}.swal2-container.swal2-grow-fullscreen>.swal2-modal{display:flex!important;flex:1;align-self:stretch;justify-content:center}.swal2-container.swal2-grow-row>.swal2-modal{display:flex!important;flex:1;align-content:center;justify-content:center}.swal2-container.swal2-grow-column{flex:1;flex-direction:column}.swal2-container.swal2-grow-column.swal2-bottom,.swal2-container.swal2-grow-column.swal2-center,.swal2-container.swal2-grow-column.swal2-top{align-items:center}.swal2-container.swal2-grow-column.swal2-bottom-left,.swal2-container.swal2-grow-column.swal2-bottom-start,.swal2-container.swal2-grow-column.swal2-center-left,.swal2-container.swal2-grow-column.swal2-center-start,.swal2-container.swal2-grow-column.swal2-top-left,.swal2-container.swal2-grow-column.swal2-top-start{align-items:flex-start}.swal2-container.swal2-grow-column.swal2-bottom-end,.swal2-container.swal2-grow-column.swal2-bottom-right,.swal2-container.swal2-grow-column.swal2-center-end,.swal2-container.swal2-grow-column.swal2-center-right,.swal2-container.swal2-grow-column.swal2-top-end,.swal2-container.swal2-grow-column.swal2-top-right{align-items:flex-end}.swal2-container.swal2-grow-column>.swal2-modal{display:flex!important;flex:1;align-content:center;justify-content:center}.swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen)>.swal2-modal{margin:auto}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-container .swal2-modal{margin:0!important}}.swal2-container.swal2-shown{background-color:rgba(0,0,0,.4)}.swal2-popup{display:none;position:relative;box-sizing:border-box;flex-direction:column;justify-content:center;width:32em;max-width:100%;padding:1.25em;border:none;border-radius:.3125em;background:#fff;font-family:inherit;font-size:1rem}.swal2-popup:focus{outline:0}.swal2-popup.swal2-loading{overflow-y:hidden}.swal2-header{display:flex;flex-direction:column;align-items:center}.swal2-title{position:relative;max-width:100%;margin:0 0 .4em;padding:0;color:#595959;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word}.swal2-actions{display:flex;z-index:1;flex-wrap:wrap;align-items:center;justify-content:center;width:100%;margin:1.25em auto 0}.swal2-actions:not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}.swal2-actions:not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0,0,0,.1),rgba(0,0,0,.1))}.swal2-actions:not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0,0,0,.2),rgba(0,0,0,.2))}.swal2-actions.swal2-loading .swal2-styled.swal2-confirm{box-sizing:border-box;width:2.5em;height:2.5em;margin:.46875em;padding:0;-webkit-animation:swal2-rotate-loading 1.5s linear 0s infinite normal;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border:.25em solid transparent;border-radius:100%;border-color:transparent;background-color:transparent!important;color:transparent;cursor:default;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.swal2-actions.swal2-loading .swal2-styled.swal2-cancel{margin-right:30px;margin-left:30px}.swal2-actions.swal2-loading :not(.swal2-styled).swal2-confirm::after{content:"";display:inline-block;width:15px;height:15px;margin-left:5px;-webkit-animation:swal2-rotate-loading 1.5s linear 0s infinite normal;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border:3px solid #999;border-radius:50%;border-right-color:transparent;box-shadow:1px 1px 1px #fff}.swal2-styled{margin:.3125em;padding:.625em 2em;box-shadow:none;font-weight:500}.swal2-styled:not([disabled]){cursor:pointer}.swal2-styled.swal2-confirm{border:0;border-radius:.25em;background:initial;background-color:#3085d6;color:#fff;font-size:1.0625em}.swal2-styled.swal2-cancel{border:0;border-radius:.25em;background:initial;background-color:#aaa;color:#fff;font-size:1.0625em}.swal2-styled:focus{outline:0;box-shadow:0 0 0 2px #fff,0 0 0 4px rgba(50,100,150,.4)}.swal2-styled::-moz-focus-inner{border:0}.swal2-footer{justify-content:center;margin:1.25em 0 0;padding:1em 0 0;border-top:1px solid #eee;color:#545454;font-size:1em}.swal2-image{max-width:100%;margin:1.25em auto}.swal2-close{position:absolute;z-index:2;top:0;right:0;justify-content:center;width:1.2em;height:1.2em;padding:0;overflow:hidden;transition:color .1s ease-out;border:none;border-radius:0;outline:initial;background:0 0;color:#ccc;font-family:serif;font-size:2.5em;line-height:1.2;cursor:pointer}.swal2-close:hover{transform:none;background:0 0;color:#f27474}.swal2-content{z-index:1;justify-content:center;margin:0;padding:0;color:#545454;font-size:1.125em;font-weight:400;line-height:normal;text-align:center;word-wrap:break-word}.swal2-checkbox,.swal2-file,.swal2-input,.swal2-radio,.swal2-select,.swal2-textarea{margin:1em auto}.swal2-file,.swal2-input,.swal2-textarea{box-sizing:border-box;width:100%;transition:border-color .3s,box-shadow .3s;border:1px solid #d9d9d9;border-radius:.1875em;background:inherit;box-shadow:inset 0 1px 1px rgba(0,0,0,.06);color:inherit;font-size:1.125em}.swal2-file.swal2-inputerror,.swal2-input.swal2-inputerror,.swal2-textarea.swal2-inputerror{border-color:#f27474!important;box-shadow:0 0 2px #f27474!important}.swal2-file:focus,.swal2-input:focus,.swal2-textarea:focus{border:1px solid #b4dbed;outline:0;box-shadow:0 0 3px #c4e6f5}.swal2-file::-webkit-input-placeholder,.swal2-input::-webkit-input-placeholder,.swal2-textarea::-webkit-input-placeholder{color:#ccc}.swal2-file::-moz-placeholder,.swal2-input::-moz-placeholder,.swal2-textarea::-moz-placeholder{color:#ccc}.swal2-file:-ms-input-placeholder,.swal2-input:-ms-input-placeholder,.swal2-textarea:-ms-input-placeholder{color:#ccc}.swal2-file::-ms-input-placeholder,.swal2-input::-ms-input-placeholder,.swal2-textarea::-ms-input-placeholder{color:#ccc}.swal2-file::placeholder,.swal2-input::placeholder,.swal2-textarea::placeholder{color:#ccc}.swal2-range{margin:1em auto;background:inherit}.swal2-range input{width:80%}.swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}.swal2-range input,.swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}.swal2-input{height:2.625em;padding:0 .75em}.swal2-input[type=number]{max-width:10em}.swal2-file{background:inherit;font-size:1.125em}.swal2-textarea{height:6.75em;padding:.75em}.swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:inherit;color:inherit;font-size:1.125em}.swal2-checkbox,.swal2-radio{align-items:center;justify-content:center;background:inherit;color:inherit}.swal2-checkbox label,.swal2-radio label{margin:0 .6em;font-size:1.125em}.swal2-checkbox input,.swal2-radio input{margin:0 .4em}.swal2-validation-message{display:none;align-items:center;justify-content:center;padding:.625em;overflow:hidden;background:#f0f0f0;color:#666;font-size:1em;font-weight:300}.swal2-validation-message::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}.swal2-icon{position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:1.25em auto 1.875em;border:.25em solid transparent;border-radius:50%;font-family:inherit;line-height:5em;cursor:default;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.swal2-icon::before{display:flex;align-items:center;height:92%;font-size:3.75em}.swal2-icon.swal2-error{border-color:#f27474}.swal2-icon.swal2-error .swal2-x-mark{position:relative;flex-grow:1}.swal2-icon.swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}.swal2-icon.swal2-warning{border-color:#facea8;color:#f8bb86}.swal2-icon.swal2-warning::before{content:"!"}.swal2-icon.swal2-info{border-color:#9de0f6;color:#3fc3ee}.swal2-icon.swal2-info::before{content:"i"}.swal2-icon.swal2-question{border-color:#c9dae1;color:#87adbd}.swal2-icon.swal2-question::before{content:"?"}.swal2-icon.swal2-question.swal2-arabic-question-mark::before{content:"؟"}.swal2-icon.swal2-success{border-color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;transform:rotate(45deg);border-radius:50%}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left]{top:-.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right]{top:-.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}.swal2-icon.swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-.25em;left:-.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}.swal2-icon.swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}.swal2-icon.swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.875em;width:1.5625em;transform:rotate(45deg)}.swal2-icon.swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}.swal2-progress-steps{align-items:center;margin:0 0 1.25em;padding:0;background:inherit;font-weight:600}.swal2-progress-steps li{display:inline-block;position:relative}.swal2-progress-steps .swal2-progress-step{z-index:20;width:2em;height:2em;border-radius:2em;background:#3085d6;color:#fff;line-height:2em;text-align:center}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#3085d6}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:#add8e6;color:#fff}.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:#add8e6}.swal2-progress-steps .swal2-progress-step-line{z-index:10;width:2.5em;height:.4em;margin:0 -1px;background:#3085d6}[class^=swal2]{-webkit-tap-highlight-color:transparent}.swal2-show{-webkit-animation:swal2-show .3s;animation:swal2-show .3s}.swal2-show.swal2-noanimation{-webkit-animation:none;animation:none}.swal2-hide{-webkit-animation:swal2-hide .15s forwards;animation:swal2-hide .15s forwards}.swal2-hide.swal2-noanimation{-webkit-animation:none;animation:none}.swal2-rtl .swal2-close{right:auto;left:0}.swal2-animate-success-icon .swal2-success-line-tip{-webkit-animation:swal2-animate-success-line-tip .75s;animation:swal2-animate-success-line-tip .75s}.swal2-animate-success-icon .swal2-success-line-long{-webkit-animation:swal2-animate-success-line-long .75s;animation:swal2-animate-success-line-long .75s}.swal2-animate-success-icon .swal2-success-circular-line-right{-webkit-animation:swal2-rotate-success-circular-line 4.25s ease-in;animation:swal2-rotate-success-circular-line 4.25s ease-in}.swal2-animate-error-icon{-webkit-animation:swal2-animate-error-icon .5s;animation:swal2-animate-error-icon .5s}.swal2-animate-error-icon .swal2-x-mark{-webkit-animation:swal2-animate-error-x-mark .5s;animation:swal2-animate-error-x-mark .5s}@supports (-ms-accelerator:true){.swal2-range input{width:100%!important}.swal2-range output{display:none}}@media all and (-ms-high-contrast:none),(-ms-high-contrast:active){.swal2-range input{width:100%!important}.swal2-range output{display:none}}@-moz-document url-prefix(){.swal2-close:focus{outline:2px solid rgba(50,100,150,.4)}}@-webkit-keyframes swal2-toast-show{0%{transform:translateY(-.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0)}}@keyframes swal2-toast-show{0%{transform:translateY(-.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0)}}@-webkit-keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@-webkit-keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@-webkit-keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}@-webkit-keyframes swal2-show{0%{transform:scale(.7)}45%{transform:scale(1.05)}80%{transform:scale(.95)}100%{transform:scale(1)}}@keyframes swal2-show{0%{transform:scale(.7)}45%{transform:scale(1.05)}80%{transform:scale(.95)}100%{transform:scale(1)}}@-webkit-keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(.5);opacity:0}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(.5);opacity:0}}@-webkit-keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.875em;width:1.5625em}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.875em;width:1.5625em}}@-webkit-keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@-webkit-keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@-webkit-keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(.4);opacity:0}50%{margin-top:1.625em;transform:scale(.4);opacity:0}80%{margin-top:-.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(.4);opacity:0}50%{margin-top:1.625em;transform:scale(.4);opacity:0}80%{margin-top:-.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@-webkit-keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0);opacity:1}}@-webkit-keyframes swal2-rotate-loading{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}@keyframes swal2-rotate-loading{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto!important}body.swal2-no-backdrop .swal2-shown{top:auto;right:auto;bottom:auto;left:auto;max-width:calc(100% - .625em * 2);background-color:transparent}body.swal2-no-backdrop .swal2-shown>.swal2-modal{box-shadow:0 0 10px rgba(0,0,0,.4)}body.swal2-no-backdrop .swal2-shown.swal2-top{top:0;left:50%;transform:translateX(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-top-left,body.swal2-no-backdrop .swal2-shown.swal2-top-start{top:0;left:0}body.swal2-no-backdrop .swal2-shown.swal2-top-end,body.swal2-no-backdrop .swal2-shown.swal2-top-right{top:0;right:0}body.swal2-no-backdrop .swal2-shown.swal2-center{top:50%;left:50%;transform:translate(-50%,-50%)}body.swal2-no-backdrop .swal2-shown.swal2-center-left,body.swal2-no-backdrop .swal2-shown.swal2-center-start{top:50%;left:0;transform:translateY(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-center-end,body.swal2-no-backdrop .swal2-shown.swal2-center-right{top:50%;right:0;transform:translateY(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-bottom{bottom:0;left:50%;transform:translateX(-50%)}body.swal2-no-backdrop .swal2-shown.swal2-bottom-left,body.swal2-no-backdrop .swal2-shown.swal2-bottom-start{bottom:0;left:0}body.swal2-no-backdrop .swal2-shown.swal2-bottom-end,body.swal2-no-backdrop .swal2-shown.swal2-bottom-right{right:0;bottom:0}@media print{body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown){overflow-y:scroll!important}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container{position:static!important}}body.swal2-toast-shown .swal2-container{background-color:transparent}body.swal2-toast-shown .swal2-container.swal2-shown{background-color:transparent}body.swal2-toast-shown .swal2-container.swal2-top{top:0;right:auto;bottom:auto;left:50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{top:0;right:0;bottom:auto;left:auto}body.swal2-toast-shown .swal2-container.swal2-top-left,body.swal2-toast-shown .swal2-container.swal2-top-start{top:0;right:auto;bottom:auto;left:0}body.swal2-toast-shown .swal2-container.swal2-center-left,body.swal2-toast-shown .swal2-container.swal2-center-start{top:50%;right:auto;bottom:auto;left:0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{top:50%;right:auto;bottom:auto;left:50%;transform:translate(-50%,-50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{top:50%;right:0;bottom:auto;left:auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-left,body.swal2-toast-shown .swal2-container.swal2-bottom-start{top:auto;right:auto;bottom:0;left:0}body.swal2-toast-shown .swal2-container.swal2-bottom{top:auto;right:auto;bottom:0;left:50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{top:auto;right:0;bottom:0;left:auto}body.swal2-toast-column .swal2-toast{flex-direction:column;align-items:stretch}body.swal2-toast-column .swal2-toast .swal2-actions{flex:1;align-self:stretch;height:2.2em;margin-top:.3125em}body.swal2-toast-column .swal2-toast .swal2-loading{justify-content:center}body.swal2-toast-column .swal2-toast .swal2-input{height:2em;margin:.3125em auto;font-size:1em}body.swal2-toast-column .swal2-toast .swal2-validation-message{font-size:1em}</style>
	<script src="/js/sistema.sdv8.js?1748887530" timestamp="force"></script>

<script async="" src="https://www.googletagmanager.com/gtag/js?id=G-LTSE0W25FK"></script>
<script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
        dataLayer.push(arguments);
    }

    gtag('js', new Date());
    gtag('config', 'G-LTSE0W25FK');
</script>
        
<style>[vw] [vw-access-button]{display:none;flex-direction:row-reverse;width:40px;height:40px;cursor:pointer;overflow:hidden;position:absolute;border-radius:8px;transition:all .5s ease;right:0;left:auto}[vw] [vw-access-button] img{max-height:40px;transition:all .5s ease;border-radius:8px;opacity:1 !important;visibility:visible !important}[vw] [vw-access-button] .access-button{width:40px;height:40px;z-index:1}[vw] [vw-access-button] .pop-up{position:absolute;height:40px;min-width:150px;z-index:0;left:0;right:auto}[vw] [vw-access-button]:hover{width:200px}[vw] [vw-access-button].isLeft{flex-direction:row;left:0;right:auto}[vw] [vw-access-button].isLeft .pop-up{left:auto;right:0}[vw] [vw-access-button].isTopOrBottom:hover{bottom:-20px;top:0;margin-right:-80px}[vw] [vw-access-button].active{display:flex}
</style><style>[vw].left [vw-plugin-wrapper]{float:left}[vw] [vw-plugin-wrapper]{position:relative;display:none;width:300px;height:100%;float:right;background:white;-webkit-box-shadow:0px 0px 15px rgba(0,0,0,0.2);-moz-box-shadow:0px 0px 15px rgba(0,0,0,0.2);box-shadow:0px 0px 15px rgba(0,0,0,0.2);border-radius:12px;-moz-border-radius:12px;-webkit-border-radius:12px}[vw] [vw-plugin-wrapper].active{display:-webkit-flex;display:flex;flex-direction:column;-webkit-flex-direction:column;height:450px;max-width:100%;min-height:100%}
</style><style>div[vw]{position:fixed;max-width:95vw;min-height:40px;min-width:40px;right:0;top:50%;transform:translateY(-50%);z-index:2147483647 !important;display:none;margin:10px !important}div[vw].enabled{display:block}div[vw].active{margin-top:-285px}div[vw].left{left:0;right:initial}
</style></head>

<body>
    <header style="max-height: 222px;">
        <div class="nav-image">
            <style>
    .acessibilidade {
        font-size: 11px !important;
        text-transform: uppercase;
        font-weight: 400;
    }

    .acessibilidade .nav-item {
        color: var(--cor-acessibilidade-default) !important;
    }

    .acessibilidade .nav-item:hover {
        color: var(--cor-acessibilidade-default) !important;
        text-decoration: underline;
    }

    .acessibilidade .btn-link {
        color: var(--cor-acessibilidade-default) !important;
    }

    .acessibilidade .btn-link:hover {
        color: var(--cor-acessibilidade-default) !important;
        text-decoration: underline;
    }

    .autenticacao .list-inline-item {
        white-space: nowrap;
    }

    .autenticacao .list-inline-item:not(:last-child) {
        margin-right: 20px !important;
    }

    #ul-acessibilidade {
        margin-left: 70px;
    }

    #ul-acessibilidade .list-inline-item {
        margin-right: 20px !important;
    }

    .acessibilidade .list-inline-item > a {
        font-size: 11px;
    }

    .acessibilidade .list-inline-item > a:hover {
        text-decoration: underline;
    }

    .acessibilidade .btn-link {
        padding: 0 !important;
    }

    .acessibilidade .nav-link {
        display: block;
        padding: 1px !important;
        margin-top: 2px;
        color: var(--cor-acessibilidade-default) !important;
    }

    .menu-superior,
    .menu-superior-mobile {
        /* opacity: 0.8 !important; */
        font-size: 12px;
        font-weight: bold;
    }

    .menu-superior a:link {
        text-decoration: none;
        padding: 0 !important;
    }

    .menu-superior .list-inline-item > a > span {
        margin-bottom: 5px;
    }

    .btn-link {
        padding: 0;
        margin-bottom: 2px;
    }

    .menu-superior > .navbar .nav-link {
        padding: 0 0.7rem;
    }

    .menu-superior > .navbar {
        border: none;
        margin-bottom: 0;
        padding: 0;
    }

    .menu-superior-mobile a > svg {
        font-size: 16px;
        margin: 0 10px;
        color: var(--cor-acessibilidade-default) !important;
    }

    @media (max-width: 1199px) {
        .autenticacao .list-inline-item:not(:last-child) {
            margin-right: 10px !important;
        }

        #ul-acessibilidade {
            margin-left: 30px;
        }

        #ul-acessibilidade .list-inline-item {
            margin-right: 10px !important;
        }
    }

    @media (min-width: 992px) {
        .menu-superior-mobile {
            display: none;
        }
    }

    @media only screen and (max-width: 991px) {

        .menu-superior {
            display: none;
        }

        .menu-superior-mobile {
            display: block;
        }

    }

    @media (max-width: 991px) {
        .row.autenticacao {
            margin-right: 0 !important;
        }
    }

    .provisorio {
        background-color: #fff6b2;
        text-align: center;
    }

    .provisorio a {
        display: block;
        padding: 3px;
    }

    .nome-usuario {
        font-weight: 350;
    }
</style>


<div class="container-fluid menu-superior border-bottom-custom acessibilidade" style="height: 25px">
    <div class="container px-0">
        <div class="row justify-content-end text-left mx-sm-0">
            <div class="col-sm-11 col-md-10 d-flex align-items-center text-left pr-0" style="height: 25px">
                <p class="text-white titulo-pagina-detran" style="font-size: 10px; padding: 2px 10px;">Esta é a nova página do Detran MG</p>
                <ul class="list-inline d-flex justify-content-end mb-0 p-0 align-items-center" id="ul-acessibilidade">
                    <li class="list-inline-item"><a href="https://gov.br/" class="nav-item nav-link" target="_blank">Transparência</a></li><li class="list-inline-item"><a href="#" class="nav-item nav-link">Acessibilidade</a></li>
                    <li class="list-inline-item">
                        <span class="navbar-text nav-item nav-link d-flex align-items-center "> Letras
                            <button type="button" class="btn btn-link mx-1 " onclick="tamanhoFonte('a')" title="Aumentar Fonte">
                                <svg class="svg-inline--fa fa-plus fa-w-14" style="font-size: 8px;" aria-hidden="true" focusable="false" data-prefix="fa" data-icon="plus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"></path></svg><!-- <i class="fa fa-plus" style="font-size: 8px"></i> -->
                            </button>

                            <button type="button" class="btn btn-link mx-1 " onclick="tamanhoFonte('d')" title="Diminuir Fonte">
                                    <svg class="svg-inline--fa fa-minus fa-w-14" style="font-size: 8px;" aria-hidden="true" focusable="false" data-prefix="fa" data-icon="minus" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M416 208H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h384c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"></path></svg><!-- <i class="fa fa-minus" style="font-size: 8px" aria-hidden="true"></i> -->
                            </button>

                            <!--                            <button type="button" class="btn btn-link mx-1" onclick="tamanhoFontePadrao()"-->
                            <!--                                    title="Tamanho Padrão">-->
                            <!--                                <i class="fas fa-font" style="font-size: 8px"></i>-->
                            <!--                            </button>-->
                        </span>
                    </li>


                    <li class="list-inline-item">
                        <a class="nav-item nav-link " href="#" onclick="atualizarContraste()">Contraste</a>
                    </li>

                        <!--  <li class="list-inline-item">-->
                        <!--      <a class="nav-item nav-link" id="enviarPorEmail" href="#">Enviar por e-mail</a>-->
                        <!--  </li>-->

                    <li class="list-inline-item ">
                        <a href="/mapa-do-site" class="nav-item nav-link" id="mapaDoSite">Mapa do Site </a>                    </li>

                    <!--                    <li class="list-inline-item">-->
                    <!--                        <a class="nav-item nav-link" href="javascript:if(window.print)window.print()">Imprimir</a>-->
                    <!--                    </li>-->

                </ul>

            </div>

            <div class="col-sm-1 col-md-2 d-flex justify-content-end" style="padding:0;">
                <ul class="list-inline mb-0 d-flex align-items-center autenticacao" style="height: 25px;">

                    
                                                <li class="list-inline-item">
                            <span class="navbar-text nav-item nav-link nome-usuario"> Bem-vindo(a) DAVI</span>
                        </li>

                        <li class="list-inline-item">
                            <a href="/usuarios/index" class="nav-link">Minha Conta</a>                        </li>
                        <li class="list-inline-item">
                            <a href="https://transito.mg.gov.br/gov-br/autenticacao/logout?logout=logout" class="nav-link nav-item link-login-logout-gov">SAIR</a>                        </li>

                    
                </ul>
            </div>
        </div>

    </div>
</div>

<div class="container-fluid menu-superior-mobile border-bottom acessibilidade">

    
        <nav class="navbar navbar-light bg-light  ">
            <a class="navbar-brand" href="#"></a>

            <a data-toggle="collapse" name="icon" href="#navbarToggleExternalContent" aria-expanded="false" aria-controls="collapseExample">
                <svg class="svg-inline--fa fa-plus-square fa-w-14" aria-hidden="true" focusable="false" data-prefix="far" data-icon="plus-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M352 240v32c0 6.6-5.4 12-12 12h-88v88c0 6.6-5.4 12-12 12h-32c-6.6 0-12-5.4-12-12v-88h-88c-6.6 0-12-5.4-12-12v-32c0-6.6 5.4-12 12-12h88v-88c0-6.6 5.4-12 12-12h32c6.6 0 12 5.4 12 12v88h88c6.6 0 12 5.4 12 12zm96-160v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h352c26.5 0 48 21.5 48 48zm-48 346V86c0-3.3-2.7-6-6-6H54c-3.3 0-6 2.7-6 6v340c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"></path></svg><!-- <i class="far fa-plus-square"></i> -->
            </a>
        </nav>

        <div class="collapse" id="navbarToggleExternalContent">
            <div class="bg-light px-4 py-2">
                <div class="row">

                    <div class="col-12">
                        <ul class="list-unstyled text-center font-weight-light">
                            <li class="list-inline-item"><a href="https://gov.br/" class="nav-item nav-link" target="_blank">Transparência</a></li><li class="list-inline-item"><a href="#" class="nav-item nav-link">Acessibilidade</a></li>
                            <li class="list-inline-item ">
                                <a href="/mapa-do-site" class="nav-item nav-link" id="mapaDoSite">Mapa do Site </a>                            </li>

                            <li class="list-inline-item">
                                <span class="navbar-text nav-item nav-link"> Letras
                                    <button type="button" class="btn btn-link" onclick="tamanhoFonte('a')">
                                        <svg class="svg-inline--fa fa-plus-square fa-w-14" aria-hidden="true" focusable="false" data-prefix="far" data-icon="plus-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M352 240v32c0 6.6-5.4 12-12 12h-88v88c0 6.6-5.4 12-12 12h-32c-6.6 0-12-5.4-12-12v-88h-88c-6.6 0-12-5.4-12-12v-32c0-6.6 5.4-12 12-12h88v-88c0-6.6 5.4-12 12-12h32c6.6 0 12 5.4 12 12v88h88c6.6 0 12 5.4 12 12zm96-160v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h352c26.5 0 48 21.5 48 48zm-48 346V86c0-3.3-2.7-6-6-6H54c-3.3 0-6 2.7-6 6v340c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"></path></svg><!-- <i class="far fa-plus-square"></i> -->
                                    </button>
                                    <button type="button" class="btn btn-link" onclick="tamanhoFonte('d')">
                                        <svg class="svg-inline--fa fa-minus-square fa-w-14" aria-hidden="true" focusable="false" data-prefix="far" data-icon="minus-square" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M108 284c-6.6 0-12-5.4-12-12v-32c0-6.6 5.4-12 12-12h232c6.6 0 12 5.4 12 12v32c0 6.6-5.4 12-12 12H108zM448 80v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h352c26.5 0 48 21.5 48 48zm-48 346V86c0-3.3-2.7-6-6-6H54c-3.3 0-6 2.7-6 6v340c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"></path></svg><!-- <i class="far fa-minus-square"></i> -->
                                    </button>
                                </span>
                            </li>
                            <li><a class="nav-item nav-link" href="#" onclick="atualizarContraste()">Contraste <svg class="svg-inline--fa fa-sun fa-w-16" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="sun" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M256 160c-52.9 0-96 43.1-96 96s43.1 96 96 96 96-43.1 96-96-43.1-96-96-96zm246.4 80.5l-94.7-47.3 33.5-100.4c4.5-13.6-8.4-26.5-21.9-21.9l-100.4 33.5-47.4-94.8c-6.4-12.8-24.6-12.8-31 0l-47.3 94.7L92.7 70.8c-13.6-4.5-26.5 8.4-21.9 21.9l33.5 100.4-94.7 47.4c-12.8 6.4-12.8 24.6 0 31l94.7 47.3-33.5 100.5c-4.5 13.6 8.4 26.5 21.9 21.9l100.4-33.5 47.3 94.7c6.4 12.8 24.6 12.8 31 0l47.3-94.7 100.4 33.5c13.6 4.5 26.5-8.4 21.9-21.9l-33.5-100.4 94.7-47.3c13-6.5 13-24.7.2-31.1zm-155.9 106c-49.9 49.9-131.1 49.9-181 0-49.9-49.9-49.9-131.1 0-181 49.9-49.9 131.1-49.9 181 0 49.9 49.9 49.9 131.1 0 181z"></path></svg><!-- <i class="fas fa-sun"></i> --></a></li>
<!--                            <li><a class="nav-item nav-link" id="enviarPorEmail" href="#">Enviar por e-mail <i-->
<!--                                        class="far fa-envelope"></i></a></li>-->
                            <!-- <li><a class="nav-item nav-link" href="javascript:if(window.print)window.print()">Imprimir <i class="fas fa-print"></i></a></li> -->
                        </ul>
                    </div>

                    <hr>

                    <div class="col-12  border-top">
                        <div class="row mt-3">


                            
                                
                                <div class="col-6">
                                    <a href="/usuarios/index" class="btn btn-primary btn-sm">Minha Conta</a>                                </div>
                                <div class="col-6">
                                    <a href="https://transito.mg.gov.br/gov-br/autenticacao/logout?logout=logout" class="nav-link nav-item link-login-logout-gov">SAIR</a>                                </div>

                                                    </div>
                        
                    </div>

                </div>
            </div>
        </div>

    

</div>

<script>
    $(document).ready(() => {

        $("a[name=icon]").click(function (event) {
            if ($(this).find('svg').attr('data-icon') == 'minus-square') {
                $(this).find('svg').attr('data-icon', 'plus-square');
            } else {
                $(this).find('svg').attr('data-icon', 'minus-square');
            }
            ;
        });
    });
</script>
            <div class="container pr-0">
    <div class="row logo_busca mx-0">
        <div class="col-lg-3 col-md-5 col-7 p-0 div-logo">
            <a href="/"><img src="/publico/img/logo/logocabealho.svg" alt="Portal Trânsito" class="img-fluid logo-detran" style="height: auto;"></a>        </div>

                    <div class="col-lg-9 col-md-7 col-5 px-0 m-0 d-flex align-items-center justify-content-end search-logo">
                <div class="col-md-6 m-0 p-0">
                    <form method="get" accept-charset="utf-8" class=" form-create" role="form" action="/buscas"><div class="form-group text"><input type="text" name="tftxzpxzzv" class="form-control" style="display:none" controle="20:29:03.602126" id="tftxzpxzzv" value=""></div><div class="form-group text"><input type="text" name="eunotxzpdqh" class="form-control" style="display:none" id="eunotxzpdqh" value="602126297508057"></div><input type="hidden" name="bgxzpvntg" class="form-control" id="bgxzpvntg" value="fzt13MGLE8eTQuzuJLTIW3qKkKmShX0jmiARYVY-tllAtW1cuQHM92FLXDb8aQOvpvRqXKPCMhCQLrFuLfmBxmrNtzJflow0UPyWIu9Pk2vQUlxMzxoQFybacumwdBBKCQhimPNhZBMNjx3boAsbk-Z1zj3OlW7Ki6O1_072jar8G9MjiozeHBbKj1Baa0Sy"><div class="form-group text"><label for="q"></label><div class="input-group"><input type="text" name="q" class="form-control col-12 search-input-nav mr-2" placeholder="O que você está procurando?" id="q"><div class="input-group-append"><button class="btn btn-light btn-barra-busca border-buscar" id="btn-buscar" type="submit"><svg aria-hidden="true" class="svg-inline--fa fa-search fa-w-16" focusable="false" data-prefix="fa" data-icon="search" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"></path></svg><!-- <i aria-hidden="true" class="fa fa-fa fa-search"></i> --></button></div></div></div><input type="hidden" name="l" class="form-control" value="1"></form>                </div>
            </div>
        
        <div class="col-lg-9 col-md-7 col-5 px-0 d-flex align-items-center div-menu">
            <div class="container-fluid">
                <nav class="navbar navbar-expand-lg bg-light navbar-light yamm">
                    <div class="container-fluid" id="menu-responsivo">

                        <button class="navbar-toggler ml-auto d-flex align-items-center d-lg-none" type="button" data-toggle="collapse" data-target="#nav" aria-controls="nav" aria-expanded="false" aria-label="Toggle navigation">
                            <span id="tx-menu">MENU</span>
                            <span class="navbar-toggler-icon"></span>
                            <span class="icon-x" style="display: none;">X</span>
                        </button>
                    </div>
                </nav>
            </div>
        </div>

    </div>
</div>

<script>
    document.querySelector('.navbar-toggler').addEventListener('click', function() {
        let icon = document.querySelector('.navbar-toggler-icon');
        let iconX = document.querySelector('.icon-x');

        icon.classList.toggle('hidden'); // Alterna a visibilidade do ícone do menu
        if (icon.classList.contains('hidden')) {
            iconX.style.display = 'inline'; // Mostra o ícone "X"
        } else {
            iconX.style.display = 'none'; // Esconde o ícone "X"
        }
    });

    window.addEventListener('resize', function() {
        if (window.innerWidth > 991) {
            let menuExpandido = document.getElementById('menu-expandido');
            let nav = document.getElementById('nav');
            let icon = document.querySelector('.navbar-toggler-icon');
            let iconX = document.querySelector('.icon-x');

            if (nav.classList.contains('show')) {
                nav.classList.remove('show');
            }

            icon.classList.remove('hidden');
            iconX.style.display = 'none';
        }
    });
</script>

<style>
    @media (max-width: 900px) {
        .search-logo {
            display: none !important;
        }
    }

    .search-input-nav {
        box-shadow: 0px 3px 7px -1px #403f3f;
        border-radius: 4px !important;
        border: 0px solid white;
    }
</style>            <br>
        </div>
    </header>
    <div class="collapse navbar-collapse custom-nav-mobile mt-0" id="nav"><!-- essa div será usada no menu mobile -->
        <!-- Links -->
        <ul class="navbar-nav container p-0">
            <li class="nav-item yamm-fw"><a href="/sobre" class="nav-link font-weight-bold">SOBRE</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/veiculos" class="nav-link font-weight-bold">VEÍCULOS</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/habilitacao" class="nav-link font-weight-bold">HABILITAÇÃO</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/infracoes" class="nav-link font-weight-bold">INFRAÇÕES</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/parceiros-credenciados" class="nav-link font-weight-bold">PARCEIROS CREDENCIADOS</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/educacao-no-transito" class="nav-link font-weight-bold">EDUCAÇÃO NO TRÂNSITO</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/atendimento" class="nav-link font-weight-bold">ATENDIMENTO</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li>        </ul>
    </div>
    <div class="menu-down text-decoration-none mb-3" style="padding: .3rem 1rem; border-top: white 0.25rem solid;">
    <div class="container">
        <div class="row d-flex justify-content-between ">
            <li class="nav-item yamm-fw"><a href="/sobre" class="nav-link font-weight-bold">SOBRE</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/veiculos" class="nav-link font-weight-bold">VEÍCULOS</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/habilitacao" class="nav-link font-weight-bold">HABILITAÇÃO</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/infracoes" class="nav-link font-weight-bold">INFRAÇÕES</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/parceiros-credenciados" class="nav-link font-weight-bold">PARCEIROS CREDENCIADOS</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/educacao-no-transito" class="nav-link font-weight-bold">EDUCAÇÃO NO TRÂNSITO</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li><li class="nav-item yamm-fw"><a href="/atendimento" class="nav-link font-weight-bold">ATENDIMENTO</a><div class="dropdown-menu yamm-content pb-1"><div class="row pt-2 px-4"></div></div></li>        </div>
    </div>
</div>
    <!-- ###### Conteudo ###### -->

        <div class="position-fixed fixed-scroll">
            <div class="col-md-2 d-flex justify-content-center align-itens-center justify-content-md-end">
        <div class="text-center">
<!--            <p class="mb-0 mt-2 mt-md-0">REDES SOCIAIS</p>-->
            <ul class="list-inline midias-sociais m-0 p-0 custom-social">
                
                        <li class="list-group m-0 p-0">
                            <a href="https://www.facebook.com/detranmg" title="CET-MG no Facebook" target="_blank" class="btn btn-just-icon btn-simple m-0 p-0">
                                <svg class="svg-inline--fa fa-facebook fa-w-16" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="facebook" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"></path></svg><!-- <i class="fab fa-facebook"></i> -->
                            </a>
                        </li>
                    
                        <li class="list-group m-0 p-0">
                            <a href="https://www.instagram.com/detranmg/" title="CET-MG no Instagram" target="_blank" class="btn btn-just-icon btn-simple m-0 p-0">
                                <svg class="svg-inline--fa fa-instagram fa-w-14" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="instagram" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"></path></svg><!-- <i class="fab fa-instagram"></i> -->
                            </a>
                        </li>
                                </ul>
        </div>
    </div>
    </div>
    <main role="main" class="flex-shrink-0">
        <div class="container servico-default">
                        
    <link rel="stylesheet" href="/css/notificacao.css?1748887530" timestamp="force"><script src="/js/cookie.js?1748887530" timestamp="force"></script>
    <div class="notificacoes">
            </div>

    <script>
        $(function() {
            $('.notificacoes .toast').each(function() {
                let $this = $(this);
                let key = $this.attr('key');

                if (getCookie(key)) {
                    return $this.remove();
                }

                $this.toast('show');
            });

            $('.notificacoes .toast .close').click(function() {
                const key = $(this).parents('.toast').attr('key');

                setCookie(key, `notificação-${key}`, 10);
            });

            $('.notificacoes .toast-body table').addClass('table table-striped');
        });
    </script>

            <div class="row">
                <div class="col-sm-12 col-12">

                    <div class="card shadow-sm mt-3">
    <div class="card-header post titulo-servico-detran">
        <div class="d-flex justify-content-between align-items-center border-0 m-0 p-0">
            <style>
    .breadcrumb {
        background: none !important;
    }

    .breadcrumb-item {
        font-size: 12px;
    }
</style>
<nav aria-label="breadcrumb">
    <small>
        <ol class="breadcrumb p-0 mb-1">
            <li class="breadcrumb-item">
                <a href="/">Início</a>
            </li>

                            <li class="breadcrumb-item">
                    <a href="/veiculos">Veículos</a>                </li>

                <li class="breadcrumb-item active" aria-current="page">
                    <span>Consulta de situação do veículo</span>
                </li>
                    </ol>
    </small>
</nav>
            <!-- <a class="btn btn-primary btn-redondo-tx-preto btn-acesso-rapido link-servicos-acesso-rapido btn-outros-servicos d-none d-md-block" href="/veiculos"> Ver outros serviços </a> -->
        </div> <!-- Corrigir a URL, não está fazendo o retorno correto dependendo do serviço -->
        <div class="menu-title post d-flex align-items-center">
            <div class="d-flex align-items-center justify-content-center mr-md-2 mr-3">
                <div class="icon-titulo-servico icon-portal icon-car-search"></div>
            </div>
            <div class="d-flex justify-content-center align-items-center">
                <h5 class="titulo-servico-detran titulo-servicos-categoria mb-0 ml-1">Consulta de situação do veículo</h5>
            </div>
        </div>
        <!-- <div class="d-md-none text-left mt-3 border-0 m-0 p-0">
            <a class="btn btn-primary btn-redondo-tx-preto btn-acesso-rapido link-servicos-acesso-rapido btn-outros-servicos" href="/veiculos "> Ver outros serviços </a>
        </div> -->
    </div>
</div>
                    <div class="card shadow-sm mt-3">
                        <div class="card-body" contenteditable="false">
                            
                            <div class="row justify-content-md-center">
                                <div id="content" class="col-12">
                                    
<!-- data por extenso -->
<div class="text-dark font-sm font-weight-bold small mb-2">
    Terça-feira, 03 de Junho de 2025 - 20 horas e 29 minutos</div>

<!-- mensagens sem autuação e multa ou quantidade de autuações(defesas) e multas. -->
<div class="alert alert-danger px-3 py-2 font-weight-bold h5" role="alert">Este Veículo tem 4 autuações e tem 8 multas.</div>
<!-- Veículo não cadastrado -->

<!-- Comunicado -->

<!-- placa -->
<div class="justify-content-center mb-3">
    <div class="placa placa-particular"><span class="numero-placa">SYM4G83</span></div></div>

<!-- Dados do Veículo -->
    <h2>Dados do Veículo</h2>
    <dl class="row">

        <dt class="col-5 text-right">Placa:</dt>
        <dd class="col-7">SYM4G83</dd>

        <dt class="col-5 text-right">Chassi:</dt>
        <dd class="col-7">9BD358ATFRYN20397</dd>

        <dt class="col-5 text-right">Renavam:</dt>
        <dd class="col-7">01378833977</dd>

        <dt class="col-5 text-right">IPVA Pago:</dt>
        <dd class="col-7">2025</dd>

        <dt class="col-5 text-right">Parcela:</dt>
        <dd class="col-7">UNICA</dd>

        <dt class="col-5 text-right">Seguro DPVAT Ano Atual Pago:</dt>
                <dd class="col-7">Não</dd>

        
        <dt class="col-5 text-right">Seguro DPVAT Anos Anteriores Pago:</dt>
        <dd class="col-7">2024: Não 
            2023: Não</dd>

        
        
        
        <dt class="col-5 text-right">Município:</dt>
        <dd class="col-7">MONTES CLAROS</dd>

        
        <dt class="col-5 text-right">Ano de Fabricação:</dt>
        <dd class="col-7">2023</dd>

        <dt class="col-5 text-right">Ano Modelo:</dt>
        <dd class="col-7">2024</dd>

        <dt class="col-5 text-right">Marca:</dt>
        <dd class="col-7">Tipo nao cadastrado / AUTOMOVEL FIAT/ARGO DRIVE 1.0</dd>

        <dt class="col-5 text-right">Taxa Licenciamento Paga:</dt>
        <dd class="col-7">2025</dd>

        <dt class="col-5 text-right">Data Licenciamento:</dt>
        <dd class="col-7">15/03/2025</dd>

        <dt class="col-5 text-right">Situação Licenciamento:</dt>
        <dd class="col-7">VEICULO LICENCIADO PARA O ANO 2025</dd>

            </dl>


<!-- defesas -->
    <h2 class="text-primary font-weight-bold bg-light px-2">Autuação</h2>
    <div id="divDefesas">
        <div class="table-responsive">
            <table class="table table-sm table-striped table-bordered">
                <tbody><tr class="bg-secondary text-light">
                    <th class="text-center">Órgão</th>
                    <th class="text-center">Quantidade</th>
                </tr>

                
                                    <tr>
                        <!-- defesa DER -->
                        <td>
                            <form name="post_683f853f900dd421663137" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-autuacoes-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="NTc1YmIxNTZiZDg2MDc5ZjY1ZjI3MGUxNDBjYzgwYzg0ODY2OTU5YzYxODRiY2U3ODUzN2E5NjczZDg4NTFmMTqRX5tOAlD6dHM13Zz5hTaw1MCVIP3TUkpm6D86yPULQMsGOwrUlGD1Q0y3dGPQxSss3JFxf2iGB9gFC+FV1VytbcGu1it6Dw+ZYiuIZyNe"><input type="hidden" name="orgao" class="form-control" value="der"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="descricao_orgao_autuacao" class="form-control" value="DER"><input type="hidden" name="codigo_orgao_defesa" class="form-control" value="113200"><input type="hidden" name="sequencial_autuacao" class="form-control" value="0"><input type="hidden" name="quantidade_defesa_der" class="form-control" value="00001"><input type="hidden" name="numero_processamento" class="form-control" value="0"><input type="hidden" name="lista_numeros_processamento" class="form-control" value="0"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="smqovxzppzngc" class="form-control" value="20:29:03.589983"><input type="hidden" name="dwctxzpxgoozt" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPpQDSkUWIzhoP-S4jewInxEcDUrTPjSlXo6UcbTruLl8-fbQgpJZvbTU5gXhVwn-SiavJs7YWOB_k_TYjWu2byHvUb78Jw2Zp15I4J4SYkd_u9Rr58fc406C28fWlP4nBTr_HLRevBOBt9s4tmtYiARy-MeRCSSzl9Pl-i5wNQ1si-YzQBrwy5DTEc15IAcR3A"><input type="hidden" name="xzpdasrarxe" class="form-control" value=""><input type="hidden" name="syjexzpsrf" class="form-control" value=""><input type="hidden" name="uexxzpcvtrxoej" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="4530c876e1d277146c401c75fcf026207911f2b5%3A_redirectPostToken%7Cchassi%7Ccodigo_orgao_defesa%7Cdescricao_orgao_autuacao%7Cdwctxzpxgoozt%7Cg-recaptcha-response%7Clista_numeros_processamento%7Cnumero_processamento%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_defesa_der%7Crenavam%7Csequencial_autuacao%7Csmqovxzppzngc%7Csyjexzpsrf%7Cuexxzpcvtrxoej%7Cxzpdasrarxe"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" onclick="document.post_683f853f900dd421663137.submit(); event.returnValue = false; return false;">DER</a>                        </td>
                        <td class="text-center">
                            1                        </td>
                    </tr>
                
                                                            <tr>
                            <td>
                                <form name="post_683f853f9042f450223279" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-autuacoes-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="ZTZhZDkwODE1NWFhMTY1YmYxODI1MWQ4ZGQ0NGYwZWMyYzk2OTczNjAxMzkzNTNkMWI1MmY1MzE2ZjA2MDllZfPGu3ltiPcvvB6CjfMJYYCmUrP2AwFAOKmhCPKZcviFyq+9S1/rqAaYQEFuvvQE3kcYPMGhBe2xlEF/i/EPmpUth17MW5FM5mdfnQRq7XDs"><input type="hidden" name="orgao" class="form-control" value="outros"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="codigo_orgao_defesa" class="form-control" value="251550"><input type="hidden" name="sequencial_autuacao" class="form-control" value="0"><input type="hidden" name="quantidade_defesa_oo" class="form-control" value="002"><input type="hidden" name="flag_orgao_notificacao" class="form-control" value="9"><input type="hidden" name="recibo_defesa_oo" class="form-control" value="013997250013997506                                                               "><input type="hidden" name="descricao_orgao_autuacao" class="form-control" value="PREF. DE: MG - SANTA LUZIA                        "><input type="hidden" name="oidxzptwke" class="form-control" value="20:29:03.590859"><input type="hidden" name="xzpeartt" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPojH_AF7OAwCP1DVHHUy3Ce3yGiLqftDPLye3VxvG3M_GIEH8yZ5dDnoYTuSd30FR_AfjTuGE34F4ExZLGDnGJpvjgq_q49pULFd4qTJUduBbt8d2z7aW76eHtErC1Z6No"><input type="hidden" name="xzpgx" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="e02bdc923989e123658dfa9690ee1c43eba24c64%3A_redirectPostToken%7Cchassi%7Ccodigo_orgao_defesa%7Cdescricao_orgao_autuacao%7Cflag_orgao_notificacao%7Cg-recaptcha-response%7Coidxzptwke%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_defesa_oo%7Crecibo_defesa_oo%7Crenavam%7Csequencial_autuacao%7Cxzpeartt%7Cxzpgx"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" onclick="document.post_683f853f9042f450223279.submit(); event.returnValue = false; return false;">PREF. DE: MG - SANTA LUZIA                        </a>                            </td>
                            <td class="text-center">
                                2                            </td>
                        </tr>
                                            <tr>
                            <td>
                                <form name="post_683f853f9067c937519324" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-autuacoes-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="ZTA2MzlkNjg4N2MxMWRlNzI4MjI1Mjg2YTY0M2YwOWQ5Mzc3YjZjZjYzNTIxZDY3OGYxNDk1MWY1ZjVlYTM2OD5Lt9oXWJnPkYdxo60VHrASLsFIOqvvsaUqz8Zn6wEO4aqPoI2Jil6CIsfJfK/aI3RRC3AYKETuiMnoZHzai+uXyN/ipteZVixE/ewWuch5"><input type="hidden" name="orgao" class="form-control" value="outros"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="codigo_orgao_defesa" class="form-control" value="250910"><input type="hidden" name="sequencial_autuacao" class="form-control" value="0"><input type="hidden" name="quantidade_defesa_oo" class="form-control" value="001"><input type="hidden" name="flag_orgao_notificacao" class="form-control" value="9"><input type="hidden" name="recibo_defesa_oo" class="form-control" value="014138849                                                                        "><input type="hidden" name="descricao_orgao_autuacao" class="form-control" value="PREF. DE: MG - RIBEIRAO DAS NEVES                 "><input type="hidden" name="lxzpu" class="form-control" value="20:29:03.591444"><input type="hidden" name="sxzpwliwzr" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPoS5jUBywuWOhJ_ljzuWgY2S-EjeSFH3nqRSgitsDquyry5N7onOALfi5Jin7e4yoWFpob2r8XZCm_v7zju2VLcytDVPySHo2IOONISWD8p4QnOzpOxG-zbAaGKFb3JrzOgtWoE7pZY9T2GPYTNxM4D"><input type="hidden" name="xzpks" class="form-control" value=""><input type="hidden" name="ixzpeuo" class="form-control" value=""><input type="hidden" name="hnawaxxzpg" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="eff7bc98433e2508b76e6c86da1efd036d2bb121%3A_redirectPostToken%7Cchassi%7Ccodigo_orgao_defesa%7Cdescricao_orgao_autuacao%7Cflag_orgao_notificacao%7Cg-recaptcha-response%7Chnawaxxzpg%7Cixzpeuo%7Clxzpu%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_defesa_oo%7Crecibo_defesa_oo%7Crenavam%7Csequencial_autuacao%7Csxzpwliwzr%7Cxzpks"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" onclick="document.post_683f853f9067c937519324.submit(); event.returnValue = false; return false;">PREF. DE: MG - RIBEIRAO DAS NEVES                 </a>                            </td>
                            <td class="text-center">
                                1                            </td>
                        </tr>
                                                </tbody></table>
        </div>
    </div>

<!-- multas -->
    <h2 class="text-primary font-weight-bold bg-light px-2">Multa</h2>
    <div id="divMultas">
        <div class="table-responsive">
            <table class="table table-sm table-striped table-bordered">
                <tbody><tr class="bg-secondary text-light">
                    <th class="text-center">Órgão</th>
                    <th class="text-center">Quantidade</th>
                    <th class="text-center">Valor</th>
                </tr>

                
                                    <tr>
                                                <td><form name="post_683f853f9088e144426878" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-multas-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="MjQzN2I1ZjRhOTAyZjVhY2M1NmFlNWNhZGM2NmZhZWFiYTIzNDk2YmFhYjI4NWQ1MjgxNGNiMTk2YmI2NmUwZYEq6XY0LbSbNlWoiQ9+nn9i0VK/nomkJ3tZOckHWLfCyC857k9FkZl+i95wN4NS90fiZu42bre//dUi9h0IHiTP/WrksCXJWvDuM7gC+pXw"><input type="hidden" name="orgao" class="form-control" value="der"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="sequencial_multa" class="form-control" value="0"><input type="hidden" name="quantidade_multa_der" class="form-control" value="2"><input type="hidden" name="codigo_orgao_multa" class="form-control" value="113200"><input type="hidden" name="numero_recibo" class="form-control" value="0"><input type="hidden" name="descricao_orgao_multa" class="form-control" value="DER"><input type="hidden" name="jxzpoa" class="form-control" value="20:29:03.591978"><input type="hidden" name="dfxzpdpt" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPo5AzGJBG-Ug_qGLjAO186JvVmKwB0n1BavqSkbbR_u7qvuGoT4Oi5vwNp7JwtpKJax5drU0j6ievY3TDKVE1CREwjF0eF9izttJfqa4EEI9XLquWYRczTN8lBbNnKfELxV0POryLRXzUmZ-E8YR5z6"><input type="hidden" name="axzpsin" class="form-control" value=""><input type="hidden" name="fxwxzprrz" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="c059fa10bfaece11a1006a29e031c1056db1e51f%3A_redirectPostToken%7Caxzpsin%7Cchassi%7Ccodigo_orgao_multa%7Cdescricao_orgao_multa%7Cdfxzpdpt%7Cfxwxzprrz%7Cg-recaptcha-response%7Cjxzpoa%7Cnumero_recibo%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_multa_der%7Crenavam%7Csequencial_multa"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" title="Exibir multas" onclick="document.post_683f853f9088e144426878.submit(); event.returnValue = false; return false;">DER</a></td>
                        <td class="text-center">2</td>
                        <td class="text-center">R$ 260,32</td>
                    </tr>
                
                                                            <tr>
                            <td><form name="post_683f853f90a5e376330079" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-multas-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="ZmY0NzMyMDg5M2VhNDdiNTFjZDYyMGNiNGU1ZGU5OTQxMDM5MDk1MTkyN2U4NDg0NWEwM2U0ZjEwMDM4NzFlZR5DoABxwIP0qiLpLy+6ePwpYb2XKXqHIo3aqTlwi/JwUbxju6IgY6qg2TTtzp9RLNPjg7MhDROG60Kbh3qYCza7ci/ucO90+5igrOw28zvp"><input type="hidden" name="orgao" class="form-control" value="outros"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="sequencial_multa" class="form-control" value="0"><input type="hidden" name="quantidade_multa_oo" class="form-control" value="002"><input type="hidden" name="codigo_orgao_multa" class="form-control" value="250910"><input type="hidden" name="numero_recibo" class="form-control" value=""><input type="hidden" name="descricao_orgao_multa" class="form-control" value="PREF. DE: MG - RIBEIRAO DAS NEVES                 "><input type="hidden" name="xzpmg" class="form-control" value="20:29:03.592449"><input type="hidden" name="xzpaehzjs" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPrmqvF3okPowjLqpT3oVYT3kmyx3bwOpM4nPT-Lnz7i4umOhQWOTmOYN0bLF9lXFLiUHpjIuUK-RHsgD8VhTFyZ5BCAeX8fSAflKa5YD0cQDsAYirB3ZuZpeDMiRHuIm_o01y_sN9l5Xax_1OjZ7XnI"><input type="hidden" name="vlgbxzpuodcoafr" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="d47619cf264c5c2730ea6ed9b8dd7bf9a56ff063%3A_redirectPostToken%7Cchassi%7Ccodigo_orgao_multa%7Cdescricao_orgao_multa%7Cg-recaptcha-response%7Cnumero_recibo%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_multa_oo%7Crenavam%7Csequencial_multa%7Cvlgbxzpuodcoafr%7Cxzpaehzjs%7Cxzpmg"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" title="Exibir multas" onclick="document.post_683f853f90a5e376330079.submit(); event.returnValue = false; return false;">PREF. DE: MG - RIBEIRAO DAS NEVES                 </a></td>
                            <td class="text-center">2</td>
                            <td class="text-center">R$ 455,55</td>
                        </tr>
                                            <tr>
                            <td><form name="post_683f853f90c0f591173540" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-multas-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="NTMwYmY4MGMzMWU3OTc3ODhlNDZmYzUzNTg3YjhlYWRhODhmYWRlNDQxN2VkZGJmYWYxMjhjN2RiYWI3OGZlNnkFM5IAMmDswWY3c6zuglCnD0IdaKGK+uSkprXyqutoA7Fk3PmgZVQpG74fQoX4tTn0wxcHSBY3Bf09uUmzMioyWkMqrP76tfqaGWHSq6zT"><input type="hidden" name="orgao" class="form-control" value="outros"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="sequencial_multa" class="form-control" value="0"><input type="hidden" name="quantidade_multa_oo" class="form-control" value="001"><input type="hidden" name="codigo_orgao_multa" class="form-control" value="243710"><input type="hidden" name="numero_recibo" class="form-control" value=""><input type="hidden" name="descricao_orgao_multa" class="form-control" value="PREF. DE: MG - CONTAGEM                           "><input type="hidden" name="xzpohsath" class="form-control" value="20:29:03.592874"><input type="hidden" name="jfxzpcltadc" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPrsUjA7Uu-24UI7WAlm9bpDZX-WNDoeV9RSEqnfvXTb1DqoUM7AJeFFf33eB2lfHMaQ3ZOkFIumx7yQJ3PAHjm1fN8Zo-Y24ol5oibpah-glrXqKNIvnNqB85CLXHV7kc3FhJTgA7Q3ybH5Hg08OpUGo7CXxlD35bWcsNgF-k0ZWQ"><input type="hidden" name="kxzpt" class="form-control" value=""><input type="hidden" name="uqxzpbg" class="form-control" value=""><input type="hidden" name="bsymjsjpuvfxzph" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="727c18b6fe43348e64aba7a3a65c01b120865f57%3A_redirectPostToken%7Cbsymjsjpuvfxzph%7Cchassi%7Ccodigo_orgao_multa%7Cdescricao_orgao_multa%7Cg-recaptcha-response%7Cjfxzpcltadc%7Ckxzpt%7Cnumero_recibo%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_multa_oo%7Crenavam%7Csequencial_multa%7Cuqxzpbg%7Cxzpohsath"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" title="Exibir multas" onclick="document.post_683f853f90c0f591173540.submit(); event.returnValue = false; return false;">PREF. DE: MG - CONTAGEM                           </a></td>
                            <td class="text-center">1</td>
                            <td class="text-center">R$ 195,23</td>
                        </tr>
                                            <tr>
                            <td><form name="post_683f853f90dc4610578560" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-multas-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="ZmMzNjBmNzQzODQ4OTIzYzc0NWQ4NTdkYTk5M2UwMWM1ZTRiYTkxNjdjODc2MDFlZDBjYmY4OTdhODk4ODVjZAjSNPx0vPQzTTdY5XbAmk+/1yVQbYAmwF9xzboAJtVCpCnywCLfE266MwqNSGD0QLDOYxvWvHq5UaSWgG0e4rBZ384+X7CvXGL+4mdcx2GN"><input type="hidden" name="orgao" class="form-control" value="outros"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="sequencial_multa" class="form-control" value="0"><input type="hidden" name="quantidade_multa_oo" class="form-control" value="002"><input type="hidden" name="codigo_orgao_multa" class="form-control" value="241230"><input type="hidden" name="numero_recibo" class="form-control" value=""><input type="hidden" name="descricao_orgao_multa" class="form-control" value="PREF. DE: MG - BELO HORIZONTE                     "><input type="hidden" name="rbsmxxzpt" class="form-control" value="20:29:03.593316"><input type="hidden" name="ebyxzpgo" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPrWKb610hLmS8_tI9SiSawoIZjt9-4ZHl02mYxcrLrh-UhpqqqwPNEcrJfSD8-aSBgGl12pTzJ9yqkd-LbQGffAA-Vo3MUwZrCwkl_ffELgsFPThp26uzWchx1-O2PTmQ4mLPWocKDlcrh1T6T-w-XS"><input type="hidden" name="ppxzpuyfa" class="form-control" value=""><input type="hidden" name="xzpnwrmavpe" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="614fcca2995e26cf5728f522fa80053fcf297f87%3A_redirectPostToken%7Cchassi%7Ccodigo_orgao_multa%7Cdescricao_orgao_multa%7Cebyxzpgo%7Cg-recaptcha-response%7Cnumero_recibo%7Corgao%7Cpesquisa%7Cplaca%7Cppxzpuyfa%7Cquantidade_multa_oo%7Crbsmxxzpt%7Crenavam%7Csequencial_multa%7Cxzpnwrmavpe"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" title="Exibir multas" onclick="document.post_683f853f90dc4610578560.submit(); event.returnValue = false; return false;">PREF. DE: MG - BELO HORIZONTE                     </a></td>
                            <td class="text-center">2</td>
                            <td class="text-center">R$ 260,32</td>
                        </tr>
                                            <tr>
                            <td><form name="post_683f853f90f8d632235836" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-multas-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="NTQ3NmVmMTk1Y2UxMzVjMThhYjk2N2ZhZTg0NTA0ODBmNWYxNTkyNzg0MDc3Y2RkNzJjOWVhYTM0YTk3NmI3Yq+iFLpTRjFS0KgDV07y5eMWYiahJ2yFJSX0/qvfmV0L914OBFxtfiVpVQDFUSvpyftvMU5gTFUDf4HmNDRuV8/bjnfF/JeU5EB/azAswsYH"><input type="hidden" name="orgao" class="form-control" value="outros"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="sequencial_multa" class="form-control" value="0"><input type="hidden" name="quantidade_multa_oo" class="form-control" value="001"><input type="hidden" name="codigo_orgao_multa" class="form-control" value="251550"><input type="hidden" name="numero_recibo" class="form-control" value=""><input type="hidden" name="descricao_orgao_multa" class="form-control" value="PREF. DE: MG - SANTA LUZIA                        "><input type="hidden" name="nfdxzpekb" class="form-control" value="20:29:03.593775"><input type="hidden" name="xzpsr" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPoSMGR8yCgKTNy1mbNRai1-iDpKibqEkHQ04k8ibk5LkZibTTXN59x1jvKaBXtRe5Apq85CMb8tCRWI1-zvAb30cRJL1l5v-qlOCMIwWBO4948TocHBP4Xedlh83mdU0iHZ3KyGa1MGsfCLeBFlSHPo"><input type="hidden" name="hpigwshxzpuoins" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="7b9b047aba3b33ab121223a98917e5ddcbe653a5%3A_redirectPostToken%7Cchassi%7Ccodigo_orgao_multa%7Cdescricao_orgao_multa%7Cg-recaptcha-response%7Chpigwshxzpuoins%7Cnfdxzpekb%7Cnumero_recibo%7Corgao%7Cpesquisa%7Cplaca%7Cquantidade_multa_oo%7Crenavam%7Csequencial_multa%7Cxzpsr"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-danger font-weight-bold" title="Exibir multas" onclick="document.post_683f853f90f8d632235836.submit(); event.returnValue = false; return false;">PREF. DE: MG - SANTA LUZIA                        </a></td>
                            <td class="text-center">1</td>
                            <td class="text-center">R$ 130,16</td>
                        </tr>
                                                </tbody></table>
        </div>
    </div>

<!-- Situação do Veículo -->
    <h2>Situação do Veículo</h2>    <div id="situacaoVeiculo" class="collapse show">

        <div class="container px-3 py-0">
                            <!-- Impedimentos -->




<!-- Restrições -->

    <h4 class="mt-3">Restrições</h4>
    
            <div class="row px-3 mb-2">- ALIENACAO FIDUCIARIA  AGENTE: BANCO PSA FINANCE BRASIL S</div>
    
    



                    </div>

        <div class="container px-3 py-0">
            
<!-- Dados do Licenciamento Ano Atual -->
<h4 class="text-primary font-weight-bold bg-light px-2">Licenciamento - Ano Atual</h4>
<div class="container px-3 py-0">

    <div class="py-1" id="situacao_licenciamento_ano_atual1" style="">
        O veículo não possui pendências. O CRLV encontra-se em processo de emissão digital e estará disponível para <a href="/veiculos/documentos-de-veiculos/emissao-do-clrv" target="_blank">impressão no portal da CET-MG.</a><br> O documento também poderá ser acessado no aplicativo Carteira Digital de Trânsito (CDT).    </div>

    <div class="py-1" id="situacao_licenciamento_ano_atual2" style="display:none;">
    </div>

    <div class="py-1" id="situacao_licenciamento_ano_atual3" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_ipva_ano_atual" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_multa" style="">VEICULO COM DEBITO DE MULTA</div>

    <div class="py-1" id="mensagem_impedimento" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_reserva_finaciamento" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_trlav_ano_atual" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_comunicacao_venda" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_csv" style="display:none;">
    </div>

    <div class="py-1" id="mensagem_licenciamento_ano_atual" style="display:none;">
    </div>

    <div class="py-1" id="sigla_ar" style="display:none;">
    </div>

    <div class="py-1" id="numero_ar" style="display:none;">
    </div>

</div>

<!-- Dados do Licenciamento Ano Anterior -->
<div id="apresentar_ano_anterior" style="display:none;">
    <h4 class="text-primary font-weight-bold bg-light mt-2 px-2">Licenciamento - Ano Anterior</h4>
    <div class="container px-3 py-0">

        <div class="py-1" id="situacao_licenciamento_ano_anterior1" style="">
            O veículo não possui pendências. O CRLV encontra-se em processo de emissão digital e estará disponível para <a href="/veiculos/documentos-de-veiculos/emissao-do-clrv" target="_blank">impressão no portal da CET-MG.</a><br> O documento também poderá ser acessado no aplicativo Carteira Digital de Trânsito (CDT).        </div>

        <div class="py-1" id="situacao_licenciamento_ano_anterior2" style="display:none;">
        </div>

        <div class="py-1" id="situacao_licenciamento_ano_anterior3" style="display:none;">
        </div>

        <div class="py-1" id="mensagem_ipva_ano_anterior" style="display:none;">
        </div>

        <div class="py-1" id="mensagem_trlav_ano_anterior" style="display:none;">
        </div>

        <div class="py-1" id="mensagem_licenciamento_ano_anterior" style="display:none;">
        </div>
        <div class="py-1" id="mensagem_sem_impedimentos" style="display: none;">
            O veículo não possui pendências para o ano anterior.
        </div>

    </div>
</div>

<br>

<div class="text-center mt-3">
    <button id="btnAtualizarSituacao" class="btn btn-primary" data-placa="SYM4G83" data-chassi="9BD358ATFRYN20397" data-renavam="01378833977">
        <span id="btnTexto">Atualizar Situação</span>
    </button>
</div>

<script>
    $(function() {
        startAjaxLoad();

        $(function() {
            consultarSituacaoVeiculo();
        });

        let tempoRestante = parseInt(localStorage.getItem("tempoRestante")) || 0;
        const TEMPO_BLOQUEIO = 300;
        let btn = $("#btnAtualizarSituacao");
        let textoBtn = $("#btnTexto");

        if (tempoRestante > 0) {
            iniciarContador(tempoRestante);
        }

        btn.on("click", function() {
            btn.prop("disabled", true);
            iniciarContador(TEMPO_BLOQUEIO);
            consultarSituacaoVeiculo();
        });

        function iniciarContador(tempo) {
            localStorage.setItem("tempoRestante", tempo);

            let intervalo = setInterval(function() {
                tempo--;
                localStorage.setItem("tempoRestante", tempo);

                let minutos = Math.floor(tempo / 60);
                let segundos = tempo % 60;
                textoBtn.text(
                    `Aguarde ${minutos}:${segundos < 10 ? "0" : ""}${segundos} para consultar novamente`
                );
                btn.prop("disabled", true);

                if (tempo <= 0) {
                    clearInterval(intervalo);
                    btn.prop("disabled", false);
                    textoBtn.text("Atualizar Situação");
                    localStorage.removeItem("tempoRestante");
                }
            }, 1000);
        }

        function atualizarCampo(id, valor, mensagemExtra = "", urlExtra = "") {
            if (valor) {
                let mensagem = valor + mensagemExtra;
                if (urlExtra) {
                    mensagem += ` <a href="${urlExtra}" target="_blank">clique aqui.</a>`;
                }
                $(`#${id}`).html(mensagem).show();
            } else {
                $(`#${id}`).hide();
            }
        }

        function consultarSituacaoVeiculo() {
            let dados = {
                placa: btn.attr("data-placa"),
                chassi: btn.attr("data-chassi"),
                renavam: btn.attr("data-renavam"),
            };
            const ultimoLicenciamento = "15/03/2025";
            const anoAtual = new Date().getFullYear();
            const anoLicenciamento = ultimoLicenciamento ? ultimoLicenciamento.split('/')[2] : null;

            $.ajax({
                url: getBaseURL() + "ajax/infracoes/consultar-infracoes-por-veiculo-condutor/ajax-consultar-nova-situacao",
                data: dados,
                dataType: "json",
                type: "POST",
                beforeSend: function(xhr) {
                    xhr.setRequestHeader("X-CSRF-Token", $('[name="_csrfToken"]').val());
                    textoBtn.text("Consultando...");
                },
                success: function(retorno) {
                    if (retorno.dadosMotivoNaoLicenciamento) {

                        textoBtn.text("Atualizar Situação");

                        let dadosAtualizados = retorno.dadosMotivoNaoLicenciamento;

                        if (dadosAtualizados.situacao_licenciamento_ano_atual && dadosAtualizados.situacao_licenciamento_ano_atual.length > 77) {
                            $("#situacao_licenciamento_ano_atual1").show();
                        } else {
                            $("#situacao_licenciamento_ano_atual1").hide();
                        }

                        atualizarCampo(
                            "situacao_licenciamento_ano_atual2",
                            dadosAtualizados.situacao_licenciamento_ano_atual &&
                            dadosAtualizados.situacao_licenciamento_ano_atual.length > 31 &&
                            dadosAtualizados.situacao_licenciamento_ano_atual.length < 77 ?
                            dadosAtualizados.situacao_licenciamento_ano_atual :
                            ""
                        );

                        atualizarCampo(
                            "situacao_licenciamento_ano_atual3",
                            dadosAtualizados.situacao_licenciamento_ano_atual &&
                            dadosAtualizados.situacao_licenciamento_ano_atual.length === 31 &&
                            !dadosAtualizados.mensagem_ipva_ano_atual &&
                            !dadosAtualizados.mensagem_trlav_ano_atual &&
                            !dadosAtualizados.mensagem_licenciamento_ano_atual ?
                            "O veículo não possui pendências no ano atual. Verifique débitos de anos anteriores." :
                            ""
                        );

                        atualizarCampo(
                            "mensagem_ipva_ano_atual",
                            dadosAtualizados.mensagem_ipva_ano_atual,
                            " - Para verificar débitos de IPVA,",
                            "https://www.fazenda.mg.gov.br/empresas/impostos/ipva/consulta-de-debitos-do-veiculo/"
                        );

                        atualizarCampo("mensagem_multa", dadosAtualizados.mensagem_multa);

                        atualizarCampo("mensagem_impedimento", dadosAtualizados.mensagem_impedimento);

                        atualizarCampo("mensagem_reserva_finaciamento", dadosAtualizados.mensagem_reserva_finaciamento);

                        atualizarCampo(
                            "mensagem_trlav_ano_atual",
                            dadosAtualizados.mensagem_trlav_ano_atual,
                            " - Para verificar débitos de Taxa de Licenciamento,",
                            "https://www.fazenda.mg.gov.br/empresas/impostos/ipva/consulta-de-debitos-do-veiculo/"
                        );

                        atualizarCampo("mensagem_comunicacao_venda", dadosAtualizados.mensagem_comunicacao_venda);

                        atualizarCampo("mensagem_csv", dadosAtualizados.mensagem_csv);

                        atualizarCampo("mensagem_licenciamento_ano_atual", dadosAtualizados.mensagem_licenciamento_ano_atual);

                        atualizarCampo("sigla_ar", dadosAtualizados.sigla_ar);

                        atualizarCampo("numero_ar", dadosAtualizados.numero_ar);

                        if (anoLicenciamento !== anoAtual.toString()) {
                            $("#apresentar_ano_anterior").show();
                        }

                        if (dadosAtualizados.situacao_licenciamento_ano_anterior.length > 77) {
                            $("#situacao_licenciamento_ano_anterior1").show();
                        } else {
                            $("#situacao_licenciamento_ano_anterior1").hide();
                        }

                        atualizarCampo("situacao_licenciamento_ano_anterior2",
                            dadosAtualizados.situacao_licenciamento_ano_anterior &&
                            dadosAtualizados.situacao_licenciamento_ano_anterior.length > 31 &&
                            dadosAtualizados.situacao_licenciamento_ano_anterior.length < 77 ?
                            dadosAtualizados.situacao_licenciamento_ano_anterior :
                            '');

                        atualizarCampo(
                            "situacao_licenciamento_ano_anterior3",
                            dadosAtualizados.situacao_licenciamento_ano_anterior &&
                            dadosAtualizados.situacao_licenciamento_ano_anterior.length === 31 &&
                            !dadosAtualizados.mensagem_ipva_ano_anterior &&
                            !dadosAtualizados.mensagem_trlav_ano_anterior &&
                            !dadosAtualizados.mensagem_licenciamento_ano_anterior ?
                            "O veículo não possui pendências no ano anterior. Verifique débitos de anos anteriores." :
                            ""
                        );

                        atualizarCampo(
                            "mensagem_ipva_ano_anterior",
                            dadosAtualizados.mensagem_ipva_ano_anterior,
                            " - Para verificar débitos de IPVA,",
                            "https://www.fazenda.mg.gov.br/empresas/impostos/ipva/consulta-de-debitos-do-veiculo/"
                        );

                        atualizarCampo(
                            "mensagem_trlav_ano_anterior",
                            dadosAtualizados.mensagem_trlav_ano_anterior,
                            ' - Para verificar débitos de Taxa de Licenciamento, <a href="https://www.fazenda.mg.gov.br/empresas/impostos/ipva/consulta-de-debitos-do-veiculo/" target="_blank">clique aqui.</a>'
                        );

                        atualizarCampo(
                            "mensagem_licenciamento_ano_anterior",
                            dadosAtualizados.mensagem_licenciamento_ano_anterior === "VEICULO COM COMUNICACAO DE VENDA" ?
                            "VEICULO COM COMUNICACAO DE VENDA" :
                            dadosAtualizados.mensagem_licenciamento_ano_anterior
                        );

                        if (
                            !dadosAtualizados.situacao_licenciamento_ano_anterior &&
                            !dadosAtualizados.mensagem_ipva_ano_anterior &&
                            !dadosAtualizados.mensagem_trlav_ano_anterior &&
                            !dadosAtualizados.mensagem_licenciamento_ano_anterior
                        ) {
                            $("#mensagem_sem_impedimentos").show();
                        } else {
                            $("#mensagem_sem_impedimentos").hide();
                        }

                    }
                },
                error: function() {
                    textoBtn.text("Atualizar Situação");
                    alerta({
                        title: "Erro ao consultar a nova situação. Tente novamente mais tarde.",
                        type: "error",
                    });
                },
            });
        }
    });
</script>
        </div>

    </div>

<br>

<!-- mais opções -->
<h4>Mais Opções</h4>


<div class="mt-1">
    <form name="post_683f853f928dd035161505" style="display:none;" method="post" action="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-historico-multas-veiculo"><input type="hidden" name="_method" class="form-control" value="POST"><input type="hidden" name="_csrfToken" class="form-control" autocomplete="off" value="cbc5f332df2bac64fae4d0c893dd42429edce257db2ae5308a2b02b292e71d591e5120813d52544b6b07f1ab77fa2ceba490308f8eed8418fdfb631c2d87f8c9"><input type="hidden" name="_redirectPostToken" class="form-control" value="M2ZiNThhM2MyNjRkMzFmZmVjMGI0MGFmMmU3ZmYzOWI5OTdjM2Q0NDgwODgyMzY2MGRlZDI4MzBjZGFkNmQ1ZRksKa1/mQuBjw19k0po5E6s6JCJxI6SRVnpRM16vg1znT22SErGs3NyVlULI9uGWrvdykXn1SJYskvZe2U9BFLU92B8YCcUo2z816VZomWH"><input type="hidden" name="placa" class="form-control" value="SYM4G83"><input type="hidden" name="chassi" class="form-control" value="9BD358ATFRYN20397"><input type="hidden" name="renavam" class="form-control" value="01378833977"><input type="hidden" name="pesquisa" class="form-control" value="veiculo"><input type="hidden" name="g-recaptcha-response" class="form-control" value="03AFcWeA6qtdM9DtbyHFmgdoA7iiskcWMcGUsNIPJeFtATr_44lk1D-036ooTCGGtRRg0k7Kv6sC6f8i-hOeR91owOrUmyYMTi6SVMssnT3t0f9yaM7Fc8usqjtn1cR4TOU27Lqkh29bDnNuZGF3j9oBf3tKMbg3QglHFSDFPt-e4MCx1H9Y-QpbIlixWhgWKsGY-HU2R-SC4D3zwgNhMWQd2jECT80U4ZXaT0yyTUAziIVXJHIP6u2bZvR-3MfND1beJHsIOP0gpsb0EaglMm9NSf1FVl2AgNZgJ_hVK5Be-V_QH8YXMBjGd_AyP-_gJxr_pbv40p1v2589K6Qk-ry_17-Uxi3MWvZuLAEnQgTHWITPVcLOk6xOZ1DvB6M19M8Qja1ZETZPz1XxpaK7P5Kp2n1ZiL72NPamwU1sWSWYb06DDxZkc_RjeBly0EV80NuhsBS4L1vYqPdR_8qGTFsc1h6dfN5TpccObpWOfvem8mP6i8Ye82PDtOU6IcG3iapnq7unLUWcOel0Hjhk5iGATBQfyCmaC1rn0K4E1ZEXJcnnnKgH3PZik6s9pyZ1BInq6Gwu4UcS9YA8jpRDYeeS3zsOFMvK7mTONkE5acXOWkDIxpUNjWmPxMZbPK0ccU8DWW5xdl_MXklyo19wAA_xJkfD6Quv-bIgsEBFYQkEhiuTEV7dD_OSuLhrMaviSMyWIpO3l-zV386GfHXgP59Y9L6f-TNHP7y9_ewJBFrofO1KUiJIZo9HcgIlYIJ_NALkst0EGmRDogSdVXIVn3fIMonY8wx4LK1gYJY25WabugbxTb2B2349-hQooy_w1s_Yueb9krbhXODmHfVXNe6YNe_QQD-nefSuntvInoTLIoHEeOacsEA3qmJPZRCqEa9yiq28RKRSRyXKxAoIuPtJR4Y7QIBDt5Gcuufm3TH4MwpXZtdAX_jIcYmc4BlNDLXrNIy5ltEM2EyWaE6UL7X_Wc3zGSOoUAQjMdbzAHhe2Nua_s80ZH6Am7qUVW8xWtdRH2UQfa6bfVlpfNzihEgN1npFGKjCGDmw"><input type="hidden" name="xzpbayfxislwqnf" class="form-control" value="20:29:03.600239"><input type="hidden" name="yqxzpeq" class="form-control" value="WYkMeef8QsbD3q_eWzSADVUo--w1SIpq7swUPuCgBPpTrISYRUICnx9z6RQDB-BhV33qIQLQieTv4IrZ-kU5K5c0ZTvRbZ7rye-4w6C-rfsWkDlZvdChFpspAxUAg-H6-l-YNw2UIQ9g4KgOhYtBJV1-ldg8VQ_QL3S1wvZDbsI4UpM8gKIwlpYXZ6lvV3yLPaC3XLWvW33nQUjtM9v7yOWErydDXOVXOT6pahFElXc"><input type="hidden" name="btfbylznxzpjf" class="form-control" value=""><input type="hidden" name="cbfldoxxzpqp" class="form-control" value=""><input type="hidden" name="uaxmoromvxzpgw" class="form-control" value=""><div style="display:none;"><input type="hidden" name="_Token[fields]" class="form-control" autocomplete="off" value="5e8f0871b896f4aa2affbcb932907d94947461b6%3A_redirectPostToken%7Cbtfbylznxzpjf%7Ccbfldoxxzpqp%7Cchassi%7Cg-recaptcha-response%7Cpesquisa%7Cplaca%7Crenavam%7Cuaxmoromvxzpgw%7Cxzpbayfxislwqnf%7Cyqxzpeq"><input type="hidden" name="_Token[unlocked]" class="form-control" autocomplete="off" value="g-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response%7Cg-recaptcha-response"></div></form><a href="#" class="text-secondary" onclick="document.post_683f853f928dd035161505.submit(); event.returnValue = false; return false;"><svg aria-hidden="true" class="svg-inline--fa fa-angle-right fa-w-8 text-secondary mr-1" focusable="false" data-prefix="fa" data-icon="angle-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" data-fa-i2svg=""><path fill="currentColor" d="M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"></path></svg><!-- <i aria-hidden="true" class="fa fa-fas fa-angle-right text-secondary mr-1"></i> -->Exibir histórico de multas - Veículos</a></div>

<div class="mt-1">
    <div class="mt-1">
        <a href="/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/" class="text-secondary"><svg aria-hidden="true" class="svg-inline--fa fa-angle-right fa-w-8 mr-1" focusable="false" data-prefix="fa" data-icon="angle-right" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" data-fa-i2svg=""><path fill="currentColor" d="M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"></path></svg><!-- <i aria-hidden="true" class="fa fa-fas fa-angle-right mr-1"></i> -->Consultar Outro Veículo</a>    </div>
</div>

<div class="mt-1">
    <div class="alert alert-warning mt-2" role="alert">
            <svg aria-hidden="true" class="svg-inline--fa fa-exclamation-triangle fa-w-18" focusable="false" data-prefix="fa" data-icon="exclamation-triangle" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" data-fa-i2svg=""><path fill="currentColor" d="M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"></path></svg><!-- <i aria-hidden="true" class="fa fa-fa fa-exclamation-triangle"></i> --> Se tiver problemas ao consultar o motivo do não licenciamento do veículo,
            acesse nosso <a href="https://transito.mg.gov.br/atendimento" target="_blank"><b>Fale Conosco</b></a>.
        </div></div>
                                </div>
                            </div>

                            
                        </div>
                    </div>
                </div>

                
            </div>
        </div><!-- Fim div container -->
    </main>

    <footer class="fixed-bottom">
        <div id="versao" v="v2.1.2"></div>
        <div class="container-fluid" style="background-color: #FFF">
    <hr>
    <div class="container">

        <div class="row d-block d-lg-none" id="acessibilidade-rodape">
    <ul class="list-unstyled list-inline text-center autenticacao">
        <li class="list-inline-item"><a href="https://gov.br/" class="nav-item nav-link" target="_blank">Transparência</a></li><li class="list-inline-item"><a href="#" class="nav-item nav-link">Acessibilidade</a></li>        <li class="list-inline-item">
            <a class="nav-item nav-link " href="#" onclick="atualizarContraste()">Contraste</a>
        </li>
        <li class="list-inline-item"><a href="/mapa-do-site" class="nav-item nav-link" id="mapaDoSite">Mapa do Site </a></li>
    </ul>
</div>        <hr>
        <div class="row" style="overflow: hidden;">
            <div class="col-md-12 d-flex justify-content-center align-items-center">
                <div class="d-flex flex-wrap justify-content-center">
                                            <div class="item mb-md-0 mx-3 mb-4 m-md-4 d-flex align-items-center">
                            <a href="https://www.mg.gov.br/" target="_blank">
                                <img class="icone-rodape img-fluid" title="Portal MG" src="/publico/img/links_uteis/logo-mg-gov-br teste2.png">
                            </a>
                        </div>
                                            <div class="item mb-md-0 mx-3 mb-4 m-md-4 d-flex align-items-center">
                            <a href="https://www.mg.gov.br/pagina/mg-app" target="_blank">
                                <img class="icone-rodape img-fluid" title="MGApp" src="/publico/img/links_uteis/mgapp.png">
                            </a>
                        </div>
                                    </div>
            </div>
        </div>


        <div class="row">
            <div class="col-md-8 offset-md-2 mb-2">
    <div class="text-center">
        <div><span style="font-size:12px">SECRETARIA DE ESTADO DE PLANEJAMENTO E GESTÃO DE MINAS GERAIS - SEPLAG</span></div>

<div><span style="font-size:12px">COORDENADORIA ESTADUAL DE GESTÃO DE TRÂNSITO /&nbsp;CET - MG&nbsp;</span></div>

<div><span style="font-size:12px">Rod. Papa João Paulo II, nº 4001&nbsp;3º andar - Edifício Gerais<br>
Bairro Serra Verde, CEP 31.630-901&nbsp;Belo Horizonte/MG</span></div>
    </div>
</div>
                <div class="col-md-2 d-flex justify-content-center align-itens-center justify-content-md-end">
        <div class="text-center">
            <p class="mb-0 mt-2 mt-md-0 rede-social">REDES SOCIAIS</p>
            <ul class="list-inline midias-sociais m-0 p-0 custom-social-mobile">
                
                        <li class="list-group m-0 p-0">
                            <a href="https://www.facebook.com/detranmg" title="CET-MG no Facebook" target="_blank" class="btn btn-just-icon btn-simple m-0 p-0">
                                <svg class="svg-inline--fa fa-facebook fa-w-16" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="facebook" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" data-fa-i2svg=""><path fill="currentColor" d="M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"></path></svg><!-- <i class="fab fa-facebook"></i> -->
                            </a>
                        </li>
                    
                        <li class="list-group m-0 p-0">
                            <a href="https://www.instagram.com/detranmg/" title="CET-MG no Instagram" target="_blank" class="btn btn-just-icon btn-simple m-0 p-0">
                                <svg class="svg-inline--fa fa-instagram fa-w-14" aria-hidden="true" focusable="false" data-prefix="fab" data-icon="instagram" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" data-fa-i2svg=""><path fill="currentColor" d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"></path></svg><!-- <i class="fab fa-instagram"></i> -->
                            </a>
                        </li>
                                </ul>
        </div>
    </div>
        </div>

        <hr class="mt-2 mt-md-0">
        <div class="row d-flex align-items-center justify-content-between">
            <div class="col-9 col-md-6 d-flex justify-content-start">
                <p class="fs-md-6 mb-0"><strong>Desenvolvido por PRODEMGE</strong></p>
            </div>
            <div class="col-3 col-md-6 d-flex justify-content-end">
<!--                -->            </div>
        </div>


    </div>
    <br>
    </div></footer>

    <input type="hidden" name="urlBase" id="urlBase" value="//transito.mg.gov.br/">
    <div class="loading" id="loading" style="display: none;"></div>

    <div vw="" class="enabled" style="left: initial; right: 0px; top: 50%; bottom: initial; transform: translateY(calc(-50% - 10px));">
    <div vw-access-button="" class="active"><img class="access-button" data-src="assets/access_icon.svg" alt="Conteúdo acessível em Libras usando o VLibras Widget com opções dos Avatares Ícaro, Hosana ou Guga." src="https://vlibras.gov.br/app//assets/access_icon.svg">
<img class="pop-up" data-src="assets/access_popup.jpg" alt="Conteúdo acessível em Libras usando o VLibras Widget com opções dos Avatares Ícaro, Hosana ou Guga." src="https://vlibras.gov.br/app//assets/access_popup.jpg"></div>
    <div vw-plugin-wrapper=""><div vp="">
  <div vp-box=""></div>
  <div vp-message-box=""></div>
  <div vp-settings=""></div>
  <div vp-dictionary=""></div>
  <div vp-settings-btn=""></div>
  <div vp-info-screen=""></div>
  <div vp-suggestion-screen=""></div>
  <div vp-translator-screen=""></div>
  <div vp-main-guide-screen=""></div>
  <div vp-suggestion-button=""></div>
  <div vp-rate-box=""></div>
  <div vp-change-avatar=""></div>
  <div vp-additional-options=""></div>
  <div vp-controls=""></div>
  <span vp-click-blocker=""></span>
</div></div>
</div>
<script src="https://vlibras.gov.br/app/vlibras-plugin.js"></script>
<script>
    new window.VLibras.Widget('https://vlibras.gov.br/app');
</script>    
            <script>
                document.cookie = 'prkMeef8QsbD3q=1in303YAPXivNaoyfrcD3l3uSrlVHzNEFjX30rModsZzXR29RUBgVH9CaJGkhSffqZrHy7WH3mBRqldducK_Xw; path=/';
            </script>
        
	<script src="/layout_portal/js/jquery.force.mask.min.js?1748887528" timestamp="force"></script>


<script>
    function checkScreenSize() {
        const div = document.getElementById('detran-servico-descricao');
        if (div) {
            if (window.innerWidth < 768) {
                div.classList.remove('p-3');
            } else {
                div.classList.add('p-3');
            }
        }


    }

    checkScreenSize();

    window.addEventListener('resize', checkScreenSize);
    document.addEventListener("DOMContentLoaded", function() {
        function ajustarPadding() {
            const passosServicos = document.querySelector(".passos-servicos");
            console.log(passosServicos)
            if (passosServicos) {
                const cardBodyAnterior = passosServicos.previousElementSibling;

                // Verifica se o elemento anterior existe e tem a classe .card-body
                if (cardBodyAnterior && cardBodyAnterior.classList.contains("card-body")) {
                    if (window.innerWidth < 768) {
                        cardBodyAnterior.style.padding = "0"; // Remove padding em telas pequenas
                    } else {
                        cardBodyAnterior.style.padding = ""; // Restaura o estilo padrão
                    }
                }
            }
        }
        $('.passos-servicos .accordion').each(function() {
            $(this).removeClass('mx-5');
        });

        ajustarPadding();


        window.addEventListener("resize", ajustarPadding);
    });

    <
    /html></script></body></html>