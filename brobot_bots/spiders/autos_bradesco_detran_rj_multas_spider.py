# -*- coding: utf-8 -*-

import os
import sys
import uuid
import urllib.parse
from time import sleep

from datetime import datetime as dt
from bs4 import BeautifulSoup
from scrapy import signals
from scrapy.http import FormRequest, Request

from brobot_bots.external_modules.utils import extract_text
from brobot_bots.external_modules.custom_errors import (<PERSON><PERSON><PERSON>,
                                                        WrongCredentials,
                                                        KnownWebsiteBug,
                                                        CaptchaNotLoaded,
                                                        Hal<PERSON>,
                                                        InvalidRequestParams,
                                                        UndefinedError,
                                                        PageMalfunction)
from brobot_bots.external_modules.external_functions import CustomSpider, trap_start_requests
from brobot_bots.external_modules import custom_messages

from brobot_bots.items import BrobotBotsItem

path = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if path not in sys.path:
    sys.path.insert(1, path)


# fixme Notice: For this spider for PDF/Linha_digitavel requests it is very important what is sent in formdata,
#  because if there is extra key inside then request will fail with empty page returned,
#  for now there are 2 fields that are most important in those requests:
#    for PDF it is CTL / chave,
#    for linha digitavel it is: CTL
#  I am not also not sure if order of keys inside form_data is important,
#  but I left it as list of tuples just to keep the same order as you go through input tags in page




class autos_bradesco_detran_rj_multas_spider(CustomSpider):
    # required scraper name
    name = "autos_bradesco_detran_rj_multas"

    custom_settings = {
        'DOWNLOAD_DELAY': 2
    }

    # initial urls
    start_url = "https://www.ib7.bradesco.com.br/ibpfdetranrj/debitoVeiculoRJGrmConsultar.do"

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """Rewriting of the spider_idle function to yield result after spider closed."""

        spider = super(autos_bradesco_detran_rj_multas_spider, cls).from_crawler(
            crawler, *args, **kwargs
        )
        crawler.signals.connect(spider.get_final_result, signals.spider_idle)
        return spider

    # @trap_init
    def __init__(self, *args, **kwargs):
        
        kwargs.update({
            "preferred_captcha_service": 'TWO_CAPTCHA',
            "max_incorrect_captcha_retries": 5,
            "max_service_retries": 5,
        })

        # get variables from CustomSpider
        super().__init__(*args, **kwargs)
        self.load_page_retries = 6
        self.retry_stats_config = [
            {
                "attr_name": 'load_page_retries',
                "max_number_of_retries": self.load_page_retries,
                "min_number_of_retries_var": 0,
            }
        ]
        if self.cnpj:
            self.login_method = 'CNPJ'
        else:
            self.login_method = 'CPF'
        self.validate_empty_keys = True
        self.proxy_required = True

    @trap_start_requests
    def start_requests(self, reset_cookies=False, reset_user_agent=False):
        if not self.valid_credentials(['renavam', 'cpf_cnpj']):
            return

        if self.review_dates:
            error = InvalidRequestParams(message=custom_messages.INVALID_DATE_FORMAT)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        
        meta = {
            'reset_cookies': reset_cookies,
            'reset_user_agent': reset_user_agent
        }

        yield Request(
            self.start_url,
            callback=self.login_me_multas,
            errback=self.errback_func,
            meta=meta,
            dont_filter=True,
        )

    def _get_static_input_fields(self, response):
        """ Get all static input tags from page """
        all_keys = response.selector.xpath("//input[@type='hidden']") + response.selector.xpath("//input[@type='text']")

        def _get_name(cell):
            return cell.xpath("./@name").get("")
        skip_keys = ["", "errorAba", "CTL", "jcaptcha_response"]
        static_form_data = {_get_name(input_tag): input_tag.xpath("./@value").get("")
                            for input_tag in all_keys if _get_name(input_tag) not in skip_keys}
        return static_form_data

    def login_me_multas(self, response):

        form_data = {}

        form_data.update(self._get_static_input_fields(response))
        renavam_name = response.selector.xpath('//input[contains(@title,"Renavam")]/@name').get("")
        image_path = response.selector.xpath('//img[@id="captchaDisp-UICaptcha-captchaImage"]/@src').get("")
        if not image_path:
            if self.load_page_retries > 0:
                self.logger.info("Couldn't find captcha url in response, retrying .....")
                self.load_page_retries -= 1
                yield from self.start_requests(reset_cookies=True, reset_user_agent=True)
            else:
                error = CaptchaNotLoaded(message=custom_messages.CAPTCHA_NOT_LOADED)
                self.logger.warning(error)
                self.errors.append(error.to_dict())
            return

        # 'CNPJ' -> 3
        # 'CPF' -> 2
        form_data.update({
             "grm.idFuncao": "3" if self.login_method == 'CNPJ' else '2'
        })
        cpf_cnpj = self.cpf_cnpj
        # Everything in form_data for login is dynamic, and they are also splitting CPF/CNPJ into separate fields,
        # because of that we need to do the same in spider, by checking max size of input field
        # we know how many characters go into that field, and we can have same logic for both CPF/CNPJ
        fields = response.selector.xpath(f"//div[@id='{self.login_method.lower()}']/input")
        for cpf_field in fields:
            size = cpf_field.xpath('./@maxlength').get('')
            if not size:
                continue
            size = int(size)
            key_name = cpf_field.xpath('./@name').get('')
            value = cpf_cnpj[:size]
            form_data[key_name] = value
            # We are removing characters we already put into form_data
            cpf_cnpj = cpf_cnpj[size:]

        img_url = "https://www.ib7.bradesco.com.br/ibpfdetranrj/" + image_path

        # get cookies to download captcha image
        cookies = self.extract_cookies(response, _for="session_list")

        img_captcha_id, imgcaptcha_txt = self._get_img_captcha_token(cookies, img_url)
        if imgcaptcha_txt is None:
            return

        form_data.update({
            "jcaptcha_response": imgcaptcha_txt,
            "grm.idSeqFuncao": "3",  # Todas selection
            renavam_name: self.renavam
        })

        login_url = (
            "https://www.ib7.bradesco.com.br/ibpfdetranrj/debitoVeiculoRJGrmListar.do"
        )

        yield FormRequest(
            url=login_url,
            formdata=form_data,
            callback=self.redirect_me_multas,
            errback=self.errback_func,
            cb_kwargs={"img_captcha_id": img_captcha_id},
            dont_filter=True,
        )

    def _assert_response(self, response, img_captcha_id=None):
        """ As we need to return just one WrongCredentials error, this should be the best way because """

        # This error is in JS code so we can't use message box to capture all errors
        # This message is always returned on page, but based on if check it is either shown or not
        if 'Captcha está incorreto' in response.text and ''''true' === "true"''' in response.text:
            if not self.report_incorrect_captcha(img_captcha_id):
                yield from self.start_requests()
            raise Halt()

        error_message = ''
        if len(response.selector.xpath("//div[@class='frameWorkArea']//table//table//td")) == 1:
            error_message = extract_text(response.selector.xpath("//div[@class='frameWorkArea']//table//table"))
            # these are sem_debitos messages, we are handling them after capturing screenshot
            if "Não há débitos pendentes. (0050)" in error_message or 'Não constam multas por' in error_message:
                error_message = ''

        if "Sistema em atualização, favor acessar mais tarde. (0131)" in response.text:
            details_msg = "Sistema em atualização, favor acessar mais tarde. (0131)"
            error = KnownWebsiteBug(message=details_msg)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            raise Halt()

        if "inválido ou não encontrado" in error_message or 'Dados Inválidos' in error_message:
            error = WrongCredentials(message=error_message)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            raise Halt()

        if "Sistema em atualização, favor acessar mais tarde. (0131)" in response.text:
            details_msg = "Sistema em atualização, favor acessar mais tarde. (0131)"
            error = KnownWebsiteBug(message=details_msg)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            raise Halt()

        # This error message was for incorrect captcha, but since now we got different error, this one means something is wrong with website
        if 'Erro no preenchimento do conteúdo da imagem' in extract_text(response.selector.xpath("//div[@class='frameWorkArea']//table//table")):
            error_message = 'Erro no preenchimento do conteúdo da imagem'
            error = KnownWebsiteBug(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            raise Halt()

        if 'Nao foi possível abrir Sessao' in error_message:
            if self.load_page_retries > 0:
                self.logger.info(error_message)
                self.load_page_retries -= 1
                sleep(3)
                yield from self.start_requests(reset_cookies=True, reset_user_agent=True)
                raise Halt()
            error = KnownWebsiteBug(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            raise Halt()

        page_was_loaded = "Não há débitos pendentes. (0050)" in response.text or \
                          response.selector.xpath('//*[@class="table-tp1"]/tbody/tr') or \
                          "Não constam multas por" in response.text
       
        if not page_was_loaded:
            error_msg = custom_messages.PAGE_MALFUNCTION or error_message 
            error = PageMalfunction(message=error_msg)
            if self.load_page_retries > 0:
                self.logger.info(error)
                self.load_page_retries -= 1
                sleep(3)
                yield from self.start_requests(reset_cookies=True, reset_user_agent=True)
                raise Halt()
            self.logger.error(error)
            self.errors.append(error.to_dict())
            raise Halt()

        cd_renavam = response.selector.xpath("//input[@name='grm.cdRenavam']/@value").get("")

        # check if captcha solved correctly; send report if not
        if not cd_renavam or error_message:
            error = UndefinedError(message=error_message)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            raise Halt()

    def redirect_me_multas(self, response, img_captcha_id=None):
        """Redirect to main page."""
        try:
            yield from self._assert_response(response, img_captcha_id=img_captcha_id)
        except Halt as _h:
            return
        if self.capture_screenshot:
            self.handle_retry_splash_engine(
                self.upload_screenshot_with_splash_engine,
                'png',
                max_retries=2,
                response=response,
                width=1300,
                html=self._clean_html(response.text)
            )

        if "Não há débitos pendentes. (0050)" in response.text:
            self.result["sem_multas"] = "Não há débitos pendentes. (0050)"
            return

        if "Não constam multas por" in response.text:
            self.result["sem_multas"] = "Não constam multas por infração de trânsito cadastradas para o documento consultado."
            return

        def format_form_data_for_pdf():
            selectors = response.selector.xpath("//div[@id='cpf']/input") + \
                response.selector.xpath("//div[@id='cnpj']/input") + \
                response.selector.xpath("//form[@name='debitoVeiculoRJForm']/input") + \
                response.selector.xpath("//div[@class='frameWorkArea']/input")
            return [(sel.xpath('./@id').get('') or sel.xpath('./@name').get(''), sel.xpath('./@value').get('')) for sel in selectors]


        multas_table = response.selector.xpath('//*[@class="table-tp1"]')

        # Auto Infração 	Placa	Data da infração	Valor	Vencimento	    Situação
        multas_table_header_row = [self.remove_diacritics(extract_text(column)) for column in multas_table.xpath(".//thead/tr/th")]

        for row in multas_table.xpath('./tbody/tr'):
            current_row = [extract_text(i) for i in row.xpath("./td")]
            row_data = dict(zip(multas_table_header_row, current_row))

            if "guia_de_recolhimento_de_multas" not in self.result:
                self.result["guia_de_recolhimento_de_multas"] = []

            # Check if we should parse this multa
            vencimento_ = dt.strptime(row_data['vencimento'], "%d/%m/%Y")
            if not self.start_date <= vencimento_ <= self.end_date:
                continue

            def format_url(format_type, nosso_numero):
                queries = {
                    'cdTipoArquivo': format_type,
                    'grm.boleto.nuNossoNumero': nosso_numero
                }
                main_url = "https://www.ib7.bradesco.com.br/ibpfdetranrj/debitoVeiculoRJGrmEmitirBoleto.do?" + urllib.parse.urlencode(queries)

                return main_url

            self.result["guia_de_recolhimento_de_multas"].append(row_data)

            if not self.get_boletos:
                continue
            
            visualizar_pdf = row.xpath('.//a[contains(@onclick,"visualizarBoleto")]/@onclick').get("")
            visualizar_pdf = visualizar_pdf.split("visualizarBoleto(")[1].split(");")[0]

            # make request to get barcode
            input_names = [
                'grm.idSeqFuncao',
                'grm.idFuncao',
                'grm.cdFuncao',
                'grm.responsavel.nuCpf1',
                'grm.responsavel.nuCpf2',
                'grm.responsavel.nuCpf3',
                'grm.responsavel.nuControleCpf',
                'grm.responsavel.nuCnpj1',
                'grm.responsavel.nuCnpj2',
                'grm.responsavel.nuCnpj3',
                'grm.responsavel.nuFilialCnpj',
                'grm.responsavel.nuControleCnpj',
                'grm.cdRenavam',
                'grm.tpPessoa',
                'grm.nuAutoInfracao',
                'chave',
                'grm.cdRestart'
            ]
            
            def extract_value_from_input(name):
                if response.selector.xpath(f"//div[contains(@class, 'frameWorkArea')]//input[@id='{name}']/@value").get(""):
                    return response.selector.xpath(f"//div[contains(@class, 'frameWorkArea')]//input[@id='{name}']/@value").get("")
                return response.selector.xpath(f"//div[contains(@class, 'frameWorkArea')]//input[@name='{name}']/@value").get("")
            # We are manually merging queries so that we keep order of queries the same
            # Build request to parse linha digitável and vencimento from boleto
            queries = [[input_field, extract_value_from_input(input_field)] for input_field in input_names]
            main_url = format_url('IMG', visualizar_pdf)
            main_url += '&' + '&'.join(['='.join(i) for i in queries])

            file_type = "__{file_type}__".format(file_type="boleto")

            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Cookie': self.extract_cookies(response, _for='header'),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': 'https://www.ib7.bradesco.com.br',
                'Referer': 'https://www.ib7.bradesco.com.br/ibpfdetranrj/debitoVeiculoRJGrmListar.do',
                'User-Agent': self.user_agent
            }

            yield Request(
                url=main_url,
                method="POST",
                callback=self.multas_codigo_barra,
                errback=self.errback_func,
                headers=headers,
                dont_filter=True,
                meta={
                    'auto_infracao': row_data['auto_infracao'],
                    'file_type': file_type
                }
            )
            
            baixar_pdf = row.xpath('.//a[contains(@onclick,"baixarPDF")]/@onclick').get("")
            # Download PDF file
            if baixar_pdf:
                baixar_pdf = baixar_pdf.split("baixarPDF(")[1].split(");")[0]

                main_url = format_url('PDF', baixar_pdf)
                form_data = format_form_data_for_pdf()

                yield FormRequest(
                    main_url,
                    callback=self.download_pdf,
                    formdata=form_data,
                    errback=self.errback_func,
                    dont_filter=True,
                    meta={
                        'index': len(self.result["guia_de_recolhimento_de_multas"]),
                        'file_type': file_type
                    }
                )


    def multas_codigo_barra(self, response):
        auto_infracao = response.meta['auto_infracao']

        linha_digitavel_codigo_barras = response.selector.xpath("//img[@id='linhaDigitavel']/@src").get("").split('=')[-1]
        vencimento = response.selector.xpath("//tr[3]/td[2]/b/text()").get('').replace(' ', '')

        text_rows = response.selector.xpath('//table[@id="Table22"]//table//td//text()').extract()
        cod_infracao = ''
        for index, row in enumerate(text_rows):
            if 'INFRACAO' in row:
                cod_infracao = text_rows[index + 1].strip().split()[1]
                break
        
        file_type = response.meta['file_type']
        html = self._clean_html_for_pdf(response)
        file_id = self.handle_retry_splash_engine(
            self.upload_pdf_with_splash_engine,
            'pdf',
            max_retries=2,
            response=response,
            ratio=0.8,
            html=html
        )

        if self.errors and f"{file_id}.pdf" in [self.errors[-1].get("file"), self.errors[-1].get("filename")]:
            self.errors.pop()
            update_values = {'file_unavailable': 'Órgão consultado não permitiu download do boleto neste momento.'}
    
        else:
            update_values = {
                "linha_digitavel_codigo_barras": linha_digitavel_codigo_barras,
                "vencimento_do_boleto": vencimento,
                "cod_infracao": cod_infracao,
                "file_id": file_id
            }

        for index, item in enumerate(self.result['guia_de_recolhimento_de_multas']):
            if auto_infracao == item.get('auto_infracao'):
                if file_type in item:
                    self.result['guia_de_recolhimento_de_multas'][index][file_type].update(update_values)
                else:
                    self.result['guia_de_recolhimento_de_multas'][index].update({file_type: update_values})

    def download_pdf(self, response):
        # options to save pdf
        index = response.meta['index']
        file_type = response.meta['file_type']
        content_type = response.headers.get('Content-Type', b'').decode().lower()
        file_id = str(uuid.uuid4())
        

        if 'pdf' in content_type:
            self.upload_pdf_with_response_body(
                response=response,
                file_id=file_id
            )
            file_data = {'file_id': file_id}
        else:
            file_data = {'file_unavailable': 'Órgão consultado não permitiu download do boleto neste momento.'}

        # This is done even when there is FileNotSaved because it will help in debugging which item is connected to this error
        if file_type in self.result['guia_de_recolhimento_de_multas'][index]:
            self.result['guia_de_recolhimento_de_multas'][index][file_type].update(file_data)
        else:
            self.result['guia_de_recolhimento_de_multas'][index].update({file_type: file_data})

    def _clean_html_for_pdf(self, response):
        soup = BeautifulSoup(response.text, 'html.parser')
        # Find all elements with the 'noprint' class and remove them
        for element in soup.find_all(class_='noprint'):
            element.decompose() 
        return str(soup)

    #             COMMON PART         #
    def _get_img_captcha_token(self, cookies, img_url):

        try:
            image_path = self.load_captcha_image(captcha_url=img_url, cookies=cookies)
        except Halt as _h:
            return None, None

        try:
            captcha_solution_id, captcha_txt =\
                self.solve_captcha("NORMAL", captcha_img=image_path)
            return captcha_solution_id, captcha_txt
        except Error as e:
            return None, None


    def _clean_html(self, html):
        soup = BeautifulSoup(html, 'html.parser')
        elements_with_style = soup.find_all(style=True)

        for element in elements_with_style:
            style = element['style']
            if 'margin-left' in style:
                new_style = ';'.join(
                    [s for s in style.split(';') if 'margin-left' not in s]
                )
                element['style'] = new_style
        
        return str(soup)
    

    def get_final_result(self, spider):
        """Will be called before spider closed
        Used to save data_collected result."""

        # stop crawling after yeild_item called
        if not self.result_received:
            if self.screenshots_ids:
                self.result["__screenshots_ids__"] = self.screenshots_ids
            # push to webhook
            self.data = {
                "scrape_id": self.scrape_id,
                "scraper_name": self.name,
                "files_count": self.files_count,
                "screenshots_count": self.screenshots_count,
                "cnpj": self.cnpj,
            }
            self.data.update({"result": self.result})
            if self.errors:
                self.data.update({"errors": self.unique_list(self.errors)})
            webhook_file_path = os.path.join(
                path,
                "downloads",
                self.scrape_id,
                "{renavam}-data_collected.json".format(
                    renavam=self.renavam.replace("/", "")
                ),
            )
            self.data_collected(self.data, webhook_file_path)
            # return item for scrapinghub
            self.result_received = True
            req = Request(
                url="http://example.com",
                callback=self.yield_item,
                errback=self.yield_item,
                dont_filter=True,
            )
            self.crawler.engine.crawl(req, spider)


    def yield_item(self, response):
        item = BrobotBotsItem()
        item.update(self.data)
        yield item
