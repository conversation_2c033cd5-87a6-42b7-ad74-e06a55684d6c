# -*- coding: utf-8 -*-
import contextlib
import json
import os
import re
import sys
import tempfile
import time
import html
import base64
from enum import Enum
from io import BytesIO
from random import random
from datetime import datetime
from hashlib import md5
from concurrent.futures import ThreadPoolExecutor

import OpenSSL
import pdftotext
from bs4 import BeautifulSoup
from seleniumwire import webdriver
from seleniumwire.utils import decode
from scrapy import Request, FormRequest, signals

from brobot_bots.items import BrobotBotsItem
from app.services.redis_service import RedisService
from brobot_bots.external_modules import custom_messages, pdf
from brobot_bots.external_modules.external_functions import (
    CustomSpider,
    trap_start_requests,
)
from brobot_bots.external_modules.custom_errors import (
    WrongCredentials,
    InvalidParameters,
    InvalidRequestParams,
    PageMalfunction,
    UserActionRequired,
    CaptchaNotSolved,
    HtmlError,
    SeleniumFail,
    NoSitekey,
    FileNotSaved,
    PDFFormatError,
    KnownWebsiteBug,
    Undefined<PERSON>ield<PERSON>ype,
    CaptchaIncorrectlySolved,
)

# Path setup for imports
path = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if path not in sys.path:
    sys.path.insert(1, path)


@contextlib.contextmanager
def pfx_to_pem(pfx, pfx_password):
    ''' Decrypts the .pfx file
        Used only because we don't want decrypted certificate file saved anywhere permanently
    '''

    with tempfile.NamedTemporaryFile(suffix='.pem') as t_pem:
        f_pem = open(t_pem.name, 'wb')
        p12 = OpenSSL.crypto.load_pkcs12(pfx, pfx_password)

        f_pem.write(OpenSSL.crypto.dump_privatekey(OpenSSL.crypto.FILETYPE_PEM, p12.get_privatekey()))
        f_pem.write(OpenSSL.crypto.dump_certificate(OpenSSL.crypto.FILETYPE_PEM, p12.get_certificate()))

        ca = p12.get_ca_certificates()
        if ca is not None:
            for cert in ca:
                f_pem.write(OpenSSL.crypto.dump_certificate(OpenSSL.crypto.FILETYPE_PEM, cert))
        f_pem.close()
        yield t_pem.name


class LoginType(Enum):
    CPF = "cpf"
    CERTIFICATE = "certificate"


class autos_detran_mg_spider(CustomSpider):
    name = "autos_detran_mg"
    HOME_URL = "https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo"
    LOGIN_URL = "https://transito.mg.gov.br/gov-br/autenticacao/login"
    SITE_SITEKEY = "6LfVpnIUAAAAAHkISk6Z6juZcsUx6hbyJGwfnfPL"

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """Rewriting of the spider_idle function to yield result after spider closed."""
        spider = super(autos_detran_mg_spider, cls).from_crawler(
            crawler, *args, **kwargs
        )
        crawler.signals.connect(spider.get_final_result, signals.spider_idle)
        return spider

    @classmethod
    def update_settings(cls, settings):
        middlewares = settings.get("DOWNLOADER_MIDDLEWARES")
        middlewares.update({"brobot_bots.middlewares.FakeUserAgentMiddleware": 501})
        settings.set("DOWNLOADER_MIDDLEWARES", middlewares, priority="spider")
        settings["AUTOTHROTTLE_ENABLED"] = False
        super().update_settings(settings)
        return settings

    def __init__(self, *args, **kwargs):
        self.senha = None
        super().__init__(*args, **kwargs)
        self.proxy_required = True
        self.proxy_service = "BRIGHTDATA_RESIDENTIAL"
        self.validate_empty_keys = True
        self.certificate = kwargs.get("request_params", {}).get("certificate_base64", None)
        self.redis_key = "autos_detran_mg_" + self.cpf
        self.cookies = {}
        self.search_result_html = None
        self.remaining_retries = 3

    @trap_start_requests
    def start_requests(self):
        if self.review_dates:
            error = InvalidRequestParams(message=custom_messages.INVALID_DATE_FORMAT)
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        if self.certificate:
            self.login_type = LoginType.CERTIFICATE
            if not self.valid_credentials(['placa', 'renavam', 'chassi', 'senha']):
                return

            # to not re-decode in spider restart
            if not isinstance(self.certificate, bytes):
                self.certificate = base64.b64decode(self.certificate)
            try:
                with pfx_to_pem(self.certificate, self.senha) as _:
                    pass
            except Exception as exc:
                self.logger.info("certificate error" + str(exc))
                message = "Certificado digital / senha ínválido ou vencido. Por favor, utilize um novo."
                error = WrongCredentials(message=message)
                self.logger.error(error)
                self.errors.append(error.to_dict())
                return
            self.redis_key = f'autos_detran_mg_{md5(self.certificate).hexdigest()}'

        else:
            self.login_type = LoginType.CPF
            credentials = ['placa', 'renavam', 'chassi', 'cpf', 'senha']
            if not self.valid_credentials(credentials):
                return
            self.redis_key = f'autos_detran_mg_{str(self.cpf)}'

        self.cookies = RedisService.get(self.redis_key, {})

        if "wrong_password" in self.cookies and self.senha == self.cookies["wrong_password"]:
            msg = self.cookies.get("error_message") or "Usuário e/ou senha inválidos. (ERL0003500)"
            error = WrongCredentials(
                message=msg,
                details="The password used is the same as one that has already received an error. Please check this",
            )
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        if not self.cookies:
            yield Request(
                url=self.HOME_URL,
                callback=self.login_path__get_govbr_url,
                errback=self.errback_func,
                dont_filter=True,
            )
        else:
            yield Request(
                url=self.HOME_URL,
                callback=self._initialize_proxy_session,
                errback=self.errback_func,
                dont_filter=True,
                cookies=self.cookies,
            )

    def login_path__get_govbr_url(self, response):
        url = "https://sso.acesso.gov.br/"
        yield Request(
            url=url,
            callback=self.login_path__redirect_to_govbr,
            errback=self.errback_func,
            dont_filter=True,
        )

    def login_path__redirect_to_govbr(self, response):
        base_url = "https://sso.acesso.gov.br/"
        random_number = round(random() * 99999)
        query_params = {
            "response_type": "code",
            "client_id": "portal-logado.estaleiro.serpro.gov.br",
            "scope": "openid+(email/phone)+profile+govbr_empresa+govbr_confiabilidades+govbr_wallet+govbr_notificacao",
            "redirect_uri": "https://servicos.acesso.gov.br/login",
            "nonce": random_number,
            # This is a semi-fixed value, is not clear what it is. Is a string like "/login" converted with JS Buffer
            "state": "eyJwYXRoIjoiLyJ9",
        }
        url = base_url + "authorize"
        url += "?" + "&".join(f"{k}={v}" for k, v in query_params.items())
        yield Request(
            url=url,
            callback=self.loging_path__redirect_to_gov_login,
            errback=self.errback_func,
            dont_filter=True,
            meta={"handle_httpstatus_list": [301, 302, 400, 403]},
        )

    def loging_path__redirect_to_gov_login(self, response):
        if response.status in [301, 302] or response.headers.get("Location"):
            url = response.headers.get("Location").decode()
            cookies = self.extract_cookies(response, _for="splash_list")
            yield Request(
                url=url,
                cookies=cookies,
                meta=response.meta,
                callback=self.login_path__parse_gov_login_page,
                errback=self.errback_func,
                dont_filter=True,
            )

    def login_path__parse_gov_login_page(self, response):
        if response.status in [301, 302] or response.headers.get("Location"):
            # Why this? After many hours of errors was discovered that when the login in Gov.br was sucessfull, and in
            # same spider session, is tryed to login again, the Gov.br site (really in cookies) recognize a previous
            # session and redirect to final page whitout need to enter the password again.
            # The reason to need login again after a sucessfull login in Gov.br is because the scrapy changes user-agent
            # in some moment after the login. And how detran site is very sensitive to user-agent, the login is lost.

            url: str = response.headers.get("Location").decode()
            # url = url if not url.startswith("/") else "https://servicos.detran.ba.gov.br/"
            cookies = self.extract_cookies(response, _for="splash_list")
            yield Request(
                url=url,
                cookies=cookies,
                meta=response.meta,
                callback=self.login_path__redirect_after_password,
                errback=self.errback_func,
                dont_filter=True,
            )
            return

        csrf_token = response.css('input[name="_csrf"]::attr(value)').get("")

        hcaptcha_token, self.user_agent = self.solve_gov_hcaptcha()
        if not hcaptcha_token:
            return

        if self.login_type == LoginType.CPF:
            form = {
                'accountId': '{}{}{}.{}{}{}.{}{}{}-{}{}'.format(*self.cpf),
                '_csrf': csrf_token,
                'operation': 'enter-account-id',
                'h-captcha-response': hcaptcha_token
            }
            authorization_id = response.url.split('=')[-1]
            url = 'https://sso.acesso.gov.br/login?client_id=servicos.detran.ba.gov.br&authorization_id=' + authorization_id
            yield FormRequest(
                url=url,
                formdata=form,
                callback=self.login_path__parse_gov_senha_page,
                errback=self.errback_func,
                dont_filter=True,
                meta={
                    'dont_redirect': True,
                    'handle_httpstatus_list': [302, 400],
                    'cached_user_agent': self.user_agent
                }
            )
            return

        form_data = {
            "_csrf": csrf_token,
            "accountId": '',  # cpf is empty since we use certificate for login
            "operation": "login-certificate",
            "h-captcha-response": hcaptcha_token
        }
        authorization_id = response.url.split('=')[-1]
        url = 'https://certificado.sso.acesso.gov.br/login?client_id=portalservicos.denatran.serpro.gov.br&authorization_id=' + authorization_id
        session = self.get_requests_session()

        cookiejarkey = response.request.meta.get("cookiejar")
        for cookie in self.cookie_jars[cookiejarkey]:
            session.cookies.set(name=cookie.name, value=cookie.value, domain=cookie.domain)
        session.headers.update({'User-Agent': self.user_agent})

        # When we use proxy we can't set session.verify = True,
        # because most of the proxy services don't check for certificado, and it always raises exceptions
        with pfx_to_pem(self.certificate, self.senha) as cert:
            sne_page = self.custom_requests(method='POST', data=form_data, url=url, session=session, cert=cert,
                                            timeout=60)
        if self.errors and self.errors[-1].get('error_type') == 'SCRAPY_ERROR':
            self.logger.warning('Error in gov login request. Possible captcha error')
            self.errors.pop()
            error = CaptchaIncorrectlySolved(
                message=custom_messages.CAPTCHA_INCORRECTLY_SOLVED,
                details='Error in gov login'
            )
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        if 'verificação em duas etapas' in sne_page.text.lower():
            error = UserActionRequired(
                message='Esta conta possui verificação em 2 etapas ativada. Necessário desativar esta verificação para que a consulta seja realizada.')
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        if 'autorização de uso de dados' in sne_page.text.lower():
            error = UserActionRequired(
                message='O login precisa de autorização de uso de dados pessoais. Por favor, autorize no portal e tente novamente.')
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        cookies = session.cookies.get_dict()
        # IMPORTANT: Only keep the necessary cookies, more than that will cause problems (break login)
        cookies_names = ['TS0197b850', 'Session_Gov_Br_Prod', 'TS0185eea4', 'INGRESSCOOKIE', 'TS01f34a2a', 'GovbrUid_YhnmzrysmhkvKqbY', 'Govbrid']
        cookies = {k: v for k, v in cookies.items() if k in cookies_names}

        RedisService.set(self.redis_key, cookies)
        self.logger.info("Got access_token")
        yield from self.start_requests()

    def login_path__parse_gov_senha_page(self, response):
        if response.status == 400:
            error_message = (
                response.css(".br-message.warning > p::text").get("").strip()
            )
            if "captcha inválido" in error_message.lower():
                if self.captcha_retries > 0:
                    self.captcha_retries -= 1
                    yield from self.start_requests()
                    return
                error = CaptchaNotSolved(
                    message=custom_messages.CAPTCHA_NOT_SOLVED, details=error_message
                )
                self.errors.append(error.to_dict())
                self.logger.error(error)
                return
            error = InvalidParameters(
                message="O campo CPF esta invalido. Favor, corrija-o.",
                details=error_message,
            )
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        if response.status == 302:
            url = response.headers.get("Location").decode()
            if "cadastro" in url:
                msg = "CPF não cadastrado."
                error = WrongCredentials(message=msg, details=msg)
                self.errors.append(error.to_dict())
                self.logger.error(error)
                return
            yield Request(
                url=url,
                callback=self.login_path__parse_gov_senha_page,
                errback=self.errback_func,
                dont_filter=True,
            )
            return

        if response.status == 403:
            if self.retries > 0:
                self.retries -= 1
                yield from self.start_requests()
                return
            error = PageMalfunction(
                message=custom_messages.PAGE_MALFUNCTION,
                details="Get 403 in senha page.",
            )
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        token = response.css('input[name="token"]::attr(value)').get("")
        csrf_token = response.css('input[name="_csrf"]::attr(value)').get("")

        form = {
            "token": token,
            "_csrf": csrf_token,
            "operation": "enter-password",
            "password": self.senha,
        }

        yield FormRequest(
            url=response.url,
            formdata=form,
            dont_filter=True,
            callback=self.login_path__redirect_after_password,
            errback=self.errback_func,
            meta={"handle_httpstatus_list": [301, 302, 400], "dont_redirect": True},
        )

    def login_path__redirect_after_password(self, response):
        if response.status == 400:
            error_message = " ".join(
                response.css(".br-message.warning > p::text").getall()
            ).strip()
            if "usuário e/ou senha inválidos" in response.text.lower():
                error_message = error_message or "Usuário e/ou senha inválidos"
                error = WrongCredentials(message=error_message, details=error_message)
                cache_var = {
                    "wrong_password": self.senha,
                    "error_message": error_message,
                }
                RedisService.set(key=self.redis_key, value=cache_var)
            elif "usuário foi bloqueado" in response.text.lower():
                error_message = error_message or "O cadastro do usuário foi bloqueado."
                error = UserActionRequired(message=error_message, details=error_message)
            else:
                if self.retries > 0:
                    self.retries -= 1
                    yield from self.start_requests()
                    return
                error = HtmlError(
                    message=error_message or custom_messages.HTML_ERROR,
                    response=response,
                    details="Error in redirect after password.",
                )
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        if response.status in [301, 302] or response.headers.get("Location"):
            url = response.headers.get("Location").decode()
            yield Request(
                url=url,
                callback=self.login_path__redirect_after_password,
                errback=self.errback_func,
                dont_filter=True,
                meta={"handle_httpstatus_list": [400]},
            )
            return

        if "Autorização de uso de dados pessoais" in response.text:
            msg = "É preciso entrar no site do Detran-MG e autorizar o acesso ao seu cadastro no Portal"
            error = UserActionRequired(message=msg)
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        cookies = dict(self.extract_cookies(response, _for="scrapy_list"))
        RedisService.set(self.redis_key, cookies)
        yield from self.start_requests()

    def _initialize_proxy_session(self, _):
        session = self.get_requests_session()
        session.cookies.update(self.cookies)

        session.get(self.HOME_URL)  # This is to get the initial cookies
        headers = {"Referer": self.HOME_URL, "User-Agent": self.user_agent}
        response = session.get(self.LOGIN_URL, headers=headers)

        # Here is logged
        cookies = session.cookies.get_dict()
        self.cookies.update(cookies)
        try:
            sitekey = response.text.split("data-sitekey=")[1].split(">")[0]
            sitekey = sitekey.replace('"', "").replace("'", "")
        except Exception as e:
            if self.remaining_retries > 0:
                self.remaining_retries -= 1
                self.logger.info("Failed to get sitekey, deleting cookies and retrying")
                RedisService.delete(key=self.redis_key)
                yield from self.start_requests()
                return
            self.logger.error(e)
            error = NoSitekey(message=custom_messages.NO_SITEKEY, details=str(e))
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        def solve_captcha():
            try:
                captcha_id, captcha_txt = self.solve_captcha(
                    captcha_type="RECAPTCHA_V2", sitekey=sitekey, captcha_url=self.LOGIN_URL
                )
                return captcha_id, captcha_txt
            except Exception as e:
                self.logger.error(e)
                return None, None

        executor = ThreadPoolExecutor(max_workers=1)
        future = executor.submit(solve_captcha)

        self.start_snapshot_engine()
        driver: webdriver.Chrome = self.snapshot_engine
        try:
            driver.get(self.HOME_URL)
            for c_name, c_value in self.cookies.items():
                driver.add_cookie({"name": c_name, "value": c_value})

            driver.get(self.HOME_URL)
            driver.find_element("css selector", "[name='chassi']").send_keys(self.chassi)
            driver.find_element("css selector", "[name='placa']").send_keys(self.placa)
            driver.find_element("css selector", "[name='renavam']").send_keys(self.renavam)

            captcha_id, captcha_txt = future.result()
            del future, executor
            if not captcha_id and not captcha_txt:
                error = CaptchaNotSolved(message=custom_messages.CAPTCHA_NOT_SOLVED)
                self.errors.append(error.to_dict())
                self.logger.error(error)
                return

            driver.execute_script(
                "document.getElementById('g-recaptcha-response').innerHTML = arguments[0];",
                captcha_txt,
            )
            driver.find_element("css selector", "form[method='post'] button[type='submit']").click()

            while not self.search_result_html:
                time.sleep(0.3)

            if 'frmRedirect' in self.search_result_html:
                # Some error, then need to capture
                self.search_result_html = driver.page_source
                time.sleep(1)
                while self.search_result_html == driver.page_source:
                    time.sleep(0.3)

            # don't use self.search_result_html because some styles in html are not applied correctly until js is loaded
            html = driver.page_source

            cookies = driver.get_cookies()
            self.cookies.update({c["name"]: c["value"] for c in cookies})

        except Exception as e:
            self.logger.error(e)
            error = SeleniumFail(message=custom_messages.SELENIUM_FAIL, details=str(e))
            self.errors.append(error.to_dict())
            self.logger.error(error)
            return

        finally:
            driver.close()
            driver.quit()

        soup = BeautifulSoup(html, "html.parser")
        alert_element = soup.select_one("div.alert.alert-danger")
        error_message = alert_element.text.replace('×', '').strip() if alert_element else ""
        # This caracter in "replace" is not an "x",        ^     is a "multiplication sign" (Unicode U+00D7)
        if "CHASSI NAO PERTENCE AO VEICULO" in html:
            default_message = "CHASSI NAO PERTENCE AO VEICULO"
            message = error_message or default_message
            error = WrongCredentials(message=message)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return

        if "Placa invalida" in html: # This many dificult to catch, because placa validation is done before in start_requests
            default_message = "Placa invalida"
            message = error_message or default_message
            error = InvalidParameters(message=message)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return

        if "RENAVAM INFORMADO INVALIDO" in html:
            default_message = "RENAVAM INFORMADO INVALIDO"
            message = error_message or default_message
            error = InvalidParameters(message=message)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return

        if "Falha na comunicação com o servidor" in html:
            default_message = "Falha na comunicação com o servidor"
            message = error_message or default_message
            error = KnownWebsiteBug(message=message)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return

        dados_veiculo = self._get_dados_veiculo(soup)
        self.result["dados_do_veiculo"] = dados_veiculo
        situacao_veiculo = self._get_situacao_veiculo(soup)
        self.result.update(situacao_veiculo)

        if self.service_name == 'cm-taxas':
            return

        if self.service_name == 'cm-frotas':
            del self.result['licenciamento_ano_atual'], self.result['licenciamento_ano_anterior']

        yield from self._get_autuacoes(soup)
        yield from self._get_multas(soup)

    def _get_dados_veiculo(self, soup):
        dl = soup.select_one("dl.row")
        data = {}
        for dt in dl.select("dt"):
            label = self.remove_diacritics(dt.text.strip())
            value = dt.find_next_sibling("dd").text.strip()
            data[label] = re.sub(r"\s+", " ", value)
        return data

    @staticmethod
    def _get_situacao_veiculo(soup):
        div = soup.select_one("div#situacaoVeiculo")

        # RESTRIÇÕES E IMPEDIMENTOS
        impedimentos, restricoes = [], []
        h4_impedimentos = div.find("h4", string="Impedimentos")
        h4_restricoes = div.find("h4", string="Restrições")
        # impedimentos
        if h4_impedimentos:
            current = h4_impedimentos.find_next_sibling()
            while current and current != h4_restricoes:
                if current.name == "div":
                    impedimentos.append(current.get_text(strip=True))
                current = current.find_next_sibling()
        # restrições
        if h4_restricoes:
            current = h4_restricoes.find_next_sibling()
            while current:
                if current.name == "div":
                    restricoes.append(current.get_text(strip=True))
                current = current.find_next_sibling()

        # LICENCIAMENTO ANO ATUAL
        label_licenciamento_atual = div.find("h4", string="Licenciamento - Ano Atual")
        if label_licenciamento_atual:
            next_div = label_licenciamento_atual.find_next_sibling("div")
            licenciamento_atual = [d.get_text(strip=True) for d in next_div.find_all("div", attrs={"style": ""})]
        else:
            licenciamento_atual = []

        # LICENCIAMENTO ANO ANTERIOR
        container_licenciamento_anterior = div.find("div", attrs={"id": "apresentar_ano_anterior", "style": ""})
        if container_licenciamento_anterior:
            label_licenciamento_anterior = div.find("h4", string="Licenciamento - Ano Anterior",)
            next_div = label_licenciamento_anterior.find_next_sibling("div")
            licenciamento_anterior = [html.unescape(d.get_text(strip=True)) for d in next_div.find_all("div", attrs={"style": ""})]
        else:
            licenciamento_anterior = []

        return {
            "impedimentos": impedimentos,
            "restricoes": restricoes,
            "licenciamento_ano_atual": licenciamento_atual,
            "licenciamento_ano_anterior": licenciamento_anterior,
        }

    def _get_autuacoes(self, soup):
        kind = "autuacoes"
        autuacoes_form = self._get_forms_next_page(soup, kind)
        if not autuacoes_form:
            self.result["sem_autuacoes"] = "Não há autuações para esse veículo no portal."
            return

        for autuacao_form in autuacoes_form.values():
            yield FormRequest(
                url="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-autuacoes-veiculo",
                formdata=autuacao_form,
                callback=self.parse_autuacao_or_multas_page,
                errback=self.errback_func,
                dont_filter=True,
                cookies=self.cookies,
                cb_kwargs={"kind": kind},
            )

    def _get_multas(self, soup):
        kind = "multas"
        multas_form = self._get_forms_next_page(soup, kind)
        if not multas_form:
            self.result["sem_multas"] = "Não há autuações para esse veículo no portal."
            return

        for multa_form in multas_form.values():
            yield FormRequest(
                url="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-multas-veiculo",
                formdata=multa_form,
                callback=self.parse_autuacao_or_multas_page,
                errback=self.errback_func,
                dont_filter=True,
                cookies=self.cookies,
                cb_kwargs={"kind": kind},
            )

    def parse_autuacao_or_multas_page(self, response, kind):
        autuacao_or_multas_forms = self._get_details_and_pdf_form(response, kind)
        csrf_token = response.css('input[name="_csrfToken"]::attr(value)').get("")
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Origin": "https://transito.mg.gov.br",
            "Pragma": "no-cache",
            "Referer": f"https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-{kind}-veiculo",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "X-CSRF-Token": csrf_token,
            "X-Requested-With": "XMLHttpRequest",
        }
        if len(autuacao_or_multas_forms) > 10:
            error = UndefinedFieldType(
                message="Novo cenário de paginação encontrado no portal. Por favor, entre em contato com a equipe de desenvolvimento.",
                details=f"Found more than 10 items for {kind}, need to check if it is need to paginate"
            )
            self.errors.append(error.to_dict())
            self.logger.error(f"Found more than 10 items for {kind}, need to check if it is need to paginate")
            self.logger.error(error)
            return

        for item in autuacao_or_multas_forms:
            data = item["item_data"]
            data_infracao = data.get('data_da_infracao')
            if data_infracao and not self.start_date <= datetime.strptime(data_infracao, "%d/%m/%Y") <= self.end_date:
                self.logger.info(f"Skipping item with id %s because it is not in the date range", data['id'])
                continue
            yield FormRequest(
                url="https://transito.mg.gov.br/ajax/infracoes/consultar-infracoes-por-veiculo-condutor/ajax-exibir-infracao-condutor",
                formdata=item["details_form"],
                callback=self.parse_details,
                errback=self.errback_func,
                dont_filter=True,
                cookies={
                    **self.cookies,
                    "csrfToken": csrf_token
                },
                headers=headers,
                cb_kwargs={
                    "boleto_form": item["boleto_form"],
                    "csrf_token": csrf_token,
                    "kind": kind,
                }
            )

    @staticmethod
    def _get_forms_next_page(html_soup, target):
        """Extract form data to navigate for multas or autuacoes pages"""
        if target not in {"autuacoes", "multas"}:
            raise ValueError(f"Invalid target: {target}")

        res = {}
        if target == "autuacoes":
            table = html_soup.select_one("div#divDefesas table tbody")
        else:
            table = html_soup.select_one("div#divMultas table tbody")

        if not table:
            return res

        for tr in table.select("tr")[1:]:
            name = re.sub(r"\s+", " ", tr.select_one("td").text.strip())
            form = tr.select_one("form")
            current = {
                input_elem["name"]: input_elem["value"]
                for input_elem in form.select("input")
            }
            res[name] = current
        return res

    def parse_details(self, response, boleto_form, csrf_token, kind):

        result = json.loads(response.text)
        self.result.setdefault(kind, []).append(result)
        if not boleto_form:
            return

        ait = result["ait"]
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-PT,pt;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://transito.mg.gov.br",
            "Pragma": "no-cache",
            "Referer": f"https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-{kind}-veiculo",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
        }

        yield FormRequest(
            url="https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/emitir-guia-multa",
            formdata=boleto_form,
            callback=self.get_pdf,
            errback=self.errback_func,
            dont_filter=True,
            cb_kwargs={"ait": ait, "kind": kind},
            cookies={**self.cookies, "csrfToken": csrf_token},
            headers=headers,
        )

    def get_pdf(self, response, ait, kind):
        if "pdf" not in response.headers.get("Content-Type", b"").decode().lower():
            return
        try:
            file_id, _, file_path = self.upload_pdf_with_response_body(response=response, return_file_path=True)
        except Exception as exc:
            error = FileNotSaved(details=str(exc), message=custom_messages.FILE_NOT_SAVE_PDF)
            self.logger.warning(error)
            self.errors.append(error.to_dict())
            return

        try:
            parsed_pdf = self._parse_pdf(response.body)
            pdf_fields = {
                'linha_digitavel_codigo_barras': pdf.validation.FieldType.CODIGO_BARRAS,
                'data_de_vencimento': pdf.validation.FieldType.DATA,
                'valor': pdf.validation.FieldType.VALOR,
            }
            if not self.is_valid_pdf(parsed_pdf, pdf_fields):
                self.logger.info(str(file_id))
                return
        except Exception as exc:
            details = f"Failed to parse pdf file with file_id {file_id}, Error details: {str(exc)}"
            error = PDFFormatError(details=details, message=custom_messages.PDF_FORMAT_ERROR)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        multa_in_result = next((item for item in self.result[kind] if item["ait"] == ait))

        file_key = "__boleto__"
        multa_in_result.update({file_key: {"file_id": file_id, **parsed_pdf}})
        return

    def _parse_pdf(self, pdf_bytes):
        pdf = pdftotext.PDF(BytesIO(pdf_bytes))

        lines = pdf[0].split("\n")
        linha_digitavel = [i for i in lines if "Linha digitável:" in i]
        linha_digitavel = linha_digitavel[0].split('Linha digitável:')[1].strip() if linha_digitavel else ''

        label = [(i, line) for i, line in enumerate(lines) if "Placa" in line][0]
        index, labels = label
        values = lines[index + 1]

        labels = re.split('\s{2,}', labels)
        labels = tuple(map(self.remove_diacritics, labels))
        values = re.split('\s{2,}', values)

        res = dict(zip(labels, values))

        return {
            'linha_digitavel_codigo_barras': linha_digitavel,
            "data_de_vencimento": res['data_de_vencimento'],
            "valor": res['valor']
        }

    def _get_details_and_pdf_form(self, response, target):
        """Extract the details form and boleto form for each multa in page"""

        if target not in {"autuacoes", "multas"}:
            raise ValueError(f"Invalid target: {target}")
        recaptcha = re.search(r"""'g-recaptcha-response':\s*?"(.*?)\"""", response.text)
        if not recaptcha:
            raise ValueError("Captcha not found")
        recaptcha = recaptcha.group(1)

        res = []
        if target == "multas":
            multas_table = response.xpath("//table[@id='tabelaMultas']")
        else:
            multas_table = response.xpath("//table[@id='tabelaAutuacoes']")

        labels = multas_table.xpath("thead//th//text()[normalize-space(.)]").getall()
        labels = tuple(map(self.remove_diacritics, labels))
        labels = labels[:-2]

        for tr in multas_table.xpath("tbody//tr"):
            item_data = tr.xpath(".//td//text()[normalize-space()]").getall()
            item_data = [item.strip() for item in item_data]
            item_data = dict(zip(labels, item_data))

            button = tr.xpath(".//button[@id='abrir-modal']")
            details_form = {
                "placa": button.xpath("@data-placa").get(""),
                "orgao": button.xpath("@data-orgao").get(""),
                "ait": button.xpath("@data-ait").get(""),
                "codigo_infracao": button.xpath("@data-codigo-infracao").get(""),
                "g-recaptcha-response": recaptcha,
            }
            form = tr.xpath(".//form")
            boleto_form = {
                input_elem.xpath("@name").get(""): input_elem.xpath("@value").get("")
                for input_elem in form.xpath(".//input")
            }
            res.append({"boleto_form": boleto_form, "details_form": details_form, 'item_data': item_data})
        return res

    def snapshot_response_interceptor(self, request, response):
        url_target = "https://transito.mg.gov.br/veiculos/situacao-do-veiculo/consulta-de-situacao-do-veiculo/exibir-dados-veiculo"
        if request.url == url_target:
            self.search_result_html = decode(
                response.body, response.headers.get("Content-Encoding", "identity")
            ).decode()

            return

    def get_final_result(self, spider):
        if not self.result_received:
            # You will probably need to change how data_collected_file_name is created
            data_collected_file_name = f"{self.renavam}-data_collected.json"  # noqa

            if self.screenshots_ids:
                self.result["__screenshots_ids__"] = self.screenshots_ids
            self.data = {  # noqa
                "scrape_id": self.scrape_id,  # noqa
                "scraper_name": self.name,
                "files_count": self.files_count,
                "screenshots_count": self.screenshots_count,
                "cnpj": self.cnpj,
                "result": self.result,
                "scraping_scope": self.scraping_scope,
            }
            if self.errors:
                self.data.update({"errors": self.unique_list(self.errors)})

            # push to webhook
            webhook_file_path = os.path.join(
                path, "downloads", self.scrape_id, data_collected_file_name
            )  # noqa N806
            self.data_collected(self.data, str(webhook_file_path))
            # return item for scrapinghub
            self.result_received = True
            req = Request(
                "https://www.example.com",
                callback=self.yield_item,
                errback=self.yield_item,
                dont_filter=True,
                meta={'proxy': None}
            )
            self.crawler.engine.crawl(req, spider)

    def yield_item(self, _):
        """Function is using to yield Scrapy Item
        Required for us to see the result in ScrapingHub"""
        item = BrobotBotsItem()
        item.update(self.data)
        yield item
