# -*- coding: utf-8 -*-

import os
import re
import sys
import base64
from urllib.parse import urlparse, parse_qs, urlencode
from datetime import datetime as dt

import pdftotext
from scrapy import signals, Selector
from scrapy.http import Request, FormRequest
from bs4 import BeautifulSoup

from brobot_bots.external_modules import custom_messages
from brobot_bots.external_modules import pdf
from brobot_bots.external_modules.credentials import cnpj
from brobot_bots.external_modules.custom_errors import (WrongCredentials,
                                                        InvalidRequestParams,
                                                        Error,
                                                        UndefinedError,
                                                        PageMalfunction,
                                                        UndefinedFieldType,
                                                        UserActionRequired)
from brobot_bots.external_modules.external_functions import CustomSpider, trap_start_requests
from brobot_bots.external_modules.utils import extract_text
from brobot_bots.items import BrobotBotsItem
from brobot_bots.spiders.services.gov_br import GovLogin, GovBrLoginType, pfx_to_pem

path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if path not in sys.path:
    sys.path.insert(1, path)


class autos_detran_pr_spider(CustomSpider):
    name = "autos_detran_pr"
    HOME_URL = "https://www.extratodebito.detran.pr.gov.br/detranextratos/geraExtrato.do?action=iniciarProcesso"
    START_LOGIN_URL = ""

    @classmethod
    def update_settings(cls, settings):
        middlewares = settings.get("DOWNLOADER_MIDDLEWARES")
        middlewares.update({"brobot_bots.middlewares.FakeUserAgentMiddleware": 501})
        settings.set("DOWNLOADER_MIDDLEWARES", middlewares, priority="spider")
        settings["AUTOTHROTTLE_ENABLED"] = False
        super().update_settings(settings)
        return settings

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """Rewriting of the spider_idle function to yield result after spider closed."""

        spider = super(autos_detran_pr_spider, cls).from_crawler(crawler, *args, **kwargs)
        crawler.signals.connect(spider.get_final_result, signals.spider_idle)
        return spider

    def __init__(self, *args, **kwargs):
        self.senha = None
        kwargs.update({
            "max_incorrect_captcha_retries": 5,
        })
        super().__init__(*args, **kwargs)
        self.validate_empty_keys = True
        self.got_fines = False
        self.get_licenciamento_after_blocked_access = False
        self.tried_unblocking = False
        self.base_html_for_screenshot = None
        self.get_homepage_retries = 3
        self.proxy_required = True
        # self.proxy_service = "BRIGHTDATA_RESIDENTIAL"
        self.e_cpf = kwargs.get('request_params', {}).get('e_cpf', None)
        self.e_cpf_password = kwargs.get('request_params', {}).get('e_cpf_password', None)

    @trap_start_requests
    def start_requests(self):
        if not self.valid_credentials(['placa', 'renavam']):
            return

        if self.review_dates:
            error = InvalidRequestParams(message=custom_messages.INVALID_DATE_FORMAT)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        self.e_cpf = base64.b64decode(self.e_cpf) if isinstance(self.e_cpf, str) else self.e_cpf

        with pfx_to_pem(self.e_cpf, self.e_cpf_password) as cert:
            print(cert)

        #  Talvez esta possa ser pulada
        yield Request(
            url=self.HOME_URL,
            callback=self.get_homepage,
            errback=self.errback_func,
            dont_filter=True
        )

    def get_homepage(self, response):
        with open('aa_home_not_logged.html', 'w') as f:
            f.write(response.text)

        gov_br_login_data = GovLogin(
            login_type=GovBrLoginType.CPF,
            cpf=self.cpf,
            password=self.senha,
            user_agent=self.user_agent,
            logger=self.logger
        ).execute()


        yield Request(
            url="https://www.extratodebito.detran.pr.gov.br/detranextratos/geraExtratoAutenticado.do?action=iniciarProcesso",
            callback=self.login_path__start_authentication,
            errback=self.errback_func,
            dont_filter=True,
            cookies=gov_br_login_data['cookies']
        )

    def login_path__start_authentication(self, response):
        """Do log in send GOV.BR cookies to site"""

        form = response.selector.css('form#formGeral')
        form_data = { input_elem.attrib['name']: input_elem.attrib.get('value', '') for input_elem in form.css('input') }
        url_parsed = urlparse(response.url)
        values_fom_url = {k: v[0] for k, v in parse_qs(url_parsed.query).items()}
        form_data.update(values_fom_url)

        form_data.pop('urlCert')
        form_data.update({
            "provedorselecionado": "tabGovbr",
            "dataAcesso": form_data.pop('acesso', ''),
            'formaAutenticacao': 'govbr',
            'provedor': 'tabGovbr'
        })

        ### Next request has a redirection to sso.acesso.gov.br, then need residential proxy
        self.proxy_session = None
        self.proxy_resolved_server = None
        self.proxy_service = "BRIGHTDATA_RESIDENTIAL"
        self.get_requests_session(url='https://lumtest.com/myip.json')  # This is just to create a proxy session

        yield FormRequest(
            url="https://auth-cs.identidadedigital.pr.gov.br/centralautenticacao/api/v1/authorize/redessociais/jwt",
            formdata=form_data,
            callback=self.login_path__get_page_for_second_login,
            errback=self.errback_func,
            dont_filter=True
        )

    def login_path__get_page_for_second_login(self, response):
        """Do log in with e-cpf"""
        ### Restore proxy to static
        self.proxy_session = None
        self.proxy_resolved_server = None
        self.proxy_service = "BRIGHTDATA_STATIC"
        session = self.get_requests_session(url='https://lumtest.com/myip.json')  # To create a proxy session

        url_parsed = urlparse(response.url)
        params = {k: v[0] for k, v in parse_qs(url_parsed.query).items()}
        url = "https://certauth-cs.identidadedigital.pr.gov.br/centralautenticacao/api/v1/certificate/authorize"

        cookiejarkey = response.request.meta.get("cookiejar")
        for cookie in self.cookie_jars[cookiejarkey]:
            session.cookies.set(name=cookie.name, value=cookie.value, domain=cookie.domain)
        session.headers.update({'User-Agent': self.user_agent})

        cs_auth = {}
        jar = self.cookie_jars[None]
        for cookie in jar:
            if cookie.domain.startswith('.auth-cs.identidadedigital.pr.gov.br'):
                cs_auth[cookie.name] = cookie.value

        cs_auth_main_value = cs_auth.pop('CS-AUTH')
        cs_auth_str = cs_auth_main_value + '; ' +  '; '.join(f'{k}={v}' for k, v in cs_auth.items()).replace('"', '')
        params.update({
            'CS-AUTH': cs_auth_str,
            'formaAutenticacao': 'btnCertificado2FA'
        })
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;'
                      'q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'pt-PT,pt;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Pragma': 'no-cache',
            'Referer': 'https://auth-cs.identidadedigital.pr.gov.br/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
        }

        with pfx_to_pem(self.e_cpf, self.e_cpf_password) as cert:
            cert_response = session.get(url, params=params, cert=cert, verify=False, headers=headers)

        if "Nenhum Certificado Digital foi reconhecido como válido." in cert_response.text:
            error = WrongCredentials(
                message="Nenhum Certificado Digital foi reconhecido como válido.",
                details="Certificado Digital inválido. Pode não ser compativel com o login do GOV.BR"
            )
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return

        for cookie in session.cookies:  # Add cookies to scrapy
            jar.set_cookie(cookie)

        soup = BeautifulSoup(cert_response.text, 'html.parser')
        form = soup.select_one('form#loginform')
        params = {input_elem['name']: input_elem.get('value', '') for input_elem in form.select('input')}

        captcha_img_src = form.select_one('img#imagemCaptcha').get('src')
        captcha_img_bytes = base64.b64decode(captcha_img_src.split(',')[1])
        with open('captcha.png', 'wb') as f:
            f.write(captcha_img_bytes)

        try:
            captcha_solution_id, imgcaptcha_txt = self.solve_captcha("NORMAL", captcha_img='captcha.png')
        except Error:
            return

        params.update({
            'renavam': self.renavam,
            'senha': imgcaptcha_txt
        })

        headers = {
            'Accept': 'text/html, */*; q=0.01',
            'Accept-Language': 'pt-PT,pt;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://www.extratodebito.detran.pr.gov.br',
            'Pragma': 'no-cache',
            'Referer': 'https://www.extratodebito.detran.pr.gov.br/detranextratos/geraExtratoAutenticado.do?action=iniciarProcesso',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
        }

        yield FormRequest(
            url='https://www.extratodebito.detran.pr.gov.br/detranextratos/geraExtratoAutenticado.do?action=viewExtract',
            formdata=params,
            headers=headers,
            callback=self.get_search_result,
            errback=self.errback_func,
            cb_kwargs={'captcha_solution_id': captcha_solution_id},
            dont_filter=True
        )

    def get_search_result(self, response, captcha_solution_id=None):
        with open('aa_home_search_result_page.html', 'w') as f:
            f.write(response.text)

    def get_final_result(self, spider):
        if not self.result_received:
            # You will probably need to change how data_collected_file_name is created
            data_collected_file_name = f'{self.renavam}-data_collected.json'

            if self.screenshots_ids:
                self.result['__screenshots_ids__'] = self.screenshots_ids
            self.data = {
                'scrape_id': self.scrape_id,
                'scraper_name': self.name,
                'files_count': self.files_count,
                'screenshots_count': self.screenshots_count,
                'cnpj': self.cnpj,
                'result': self.result,
                'scraping_scope': self.scraping_scope
            }
            if self.errors:
                self.data.update({'errors': self.unique_list(self.errors)})

            webhook_file_path = os.path.join(path, "downloads", self.scrape_id, data_collected_file_name)
            self.data_collected(self.data, webhook_file_path)
            self.result_received = True
            req = Request(
                'http://example.com',
                callback=self.yield_item,
                errback=self.yield_item,
                dont_filter=True
            )
            self.crawler.engine.crawl(req, spider)

    def yield_item(self, _):
        """Function is using to yield Scrapy Item. Required for us to see the result in ScrapingHub"""
        item = BrobotBotsItem()
        item.update(self.data)
        yield item


    @staticmethod
    def parse_html(response):
        result = {}

        # RESUMO
        resumo_table = response.css('#content-resumo table.table-striped')
        if resumo_table:
            resumo_data = []
            for tr in resumo_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 2:
                    discriminacao = tds[0].css('::text').get('').strip()
                    valor = tds[1].css('::text').get('').strip()
                    if discriminacao and valor:
                        resumo_data.append({
                            'discriminacao': discriminacao,
                            'valor': valor
                        })
            result['resumo'] = resumo_data

        # IPVA
        ipva_table = response.css('#content-ipva table.table-striped')
        if ipva_table:
            ipva_data = []
            for tr in ipva_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 2:
                    discriminacao = tds[0].css('::text').get('').strip()
                    valor = tds[1].css('::text').get('').strip()
                    if discriminacao and valor and 'Total' not in discriminacao:
                        ipva_data.append({
                            'discriminacao': discriminacao,
                            'valor': valor
                        })
            result['ipva'] = ipva_data

        # LICENCIAMENTO
        licenciamento_table = response.css('#content-licenciamento table.table-striped')
        if licenciamento_table:
            licenciamento_data = []
            for tr in licenciamento_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 3:
                    discriminacao = tds[0].css('::text').get('').strip()
                    vencimento = tds[1].css('::text').get('').strip()
                    valor = tds[2].css('::text').get('').strip()
                    if discriminacao and valor and 'TOTAL' not in discriminacao:
                        licenciamento_data.append({
                            'discriminacao': discriminacao,
                            'vencimento': vencimento,
                            'valor': valor
                        })
            result['licenciamento'] = licenciamento_data

        # MULTAS - Resumo das Multas de Trânsito
        multas_resumo_table = response.css('#content-multas_multas table.table-striped').first()
        if multas_resumo_table:
            multas_resumo_data = []
            for tr in multas_resumo_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 3:
                    discriminacao = tds[0].css('::text').get('').strip()
                    quantidade = tds[1].css('::text').get('').strip()
                    valor = tds[2].css('::text').get('').strip()
                    if discriminacao and 'TOTAL' not in discriminacao:
                        multas_resumo_data.append({
                            'discriminacao': discriminacao,
                            'quantidade': quantidade,
                            'valor': valor
                        })
            result['multas_resumo'] = multas_resumo_data

        # MULTAS - Resumo das Autuações de Trânsito
        autuacoes_table = response.css('#content-multas_multas table.table-striped').eq(1)
        if autuacoes_table:
            autuacoes_data = []
            for tr in autuacoes_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 3:
                    discriminacao = tds[0].css('::text').get('').strip()
                    quantidade = tds[1].css('::text').get('').strip()
                    valor = tds[2].css('::text').get('').strip()
                    if discriminacao and 'TOTAL' not in discriminacao:
                        autuacoes_data.append({
                            'discriminacao': discriminacao,
                            'quantidade': quantidade,
                            'valor': valor
                        })
            result['autuacoes'] = autuacoes_data

        # MULTAS PAGAS
        multas_pagas_table = response.css('#content-multas_pagas table#table-multas-pagas')
        if multas_pagas_table:
            multas_pagas_data = []
            for tr in multas_pagas_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 4:
                    data = tds[0].css('::text').get('').strip()
                    infracao = tds[1].css('::text').get('').strip()
                    data_pagamento = tds[2].css('::text').get('').strip()
                    valor_pago = tds[3].css('::text').get('').strip()
                    if data and infracao:
                        multas_pagas_data.append({
                            'data': data,
                            'infracao': infracao,
                            'data_pagamento': data_pagamento,
                            'valor_pago': valor_pago
                        })
            result['multas_pagas'] = multas_pagas_data

        # EMISSÃO LICENCIAMENTO - Débitos para emissão do CRLV
        crlv_debitos_table = response.css('#content-emissao-licenciamento table.table-striped').first()
        if crlv_debitos_table:
            crlv_debitos_data = []
            for tr in crlv_debitos_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 2:
                    discriminacao = tds[0].css('::text').get('').strip()
                    valor = tds[1].css('::text').get('').strip()
                    if discriminacao and valor:
                        crlv_debitos_data.append({
                            'discriminacao': discriminacao,
                            'valor': valor
                        })
            result['crlv_debitos'] = crlv_debitos_data

        # EMISSÃO LICENCIAMENTO - Últimos Pagamentos do Licenciamento
        pagamentos_licenciamento_table = response.css('#content-emissao-licenciamento table.table-striped').eq(1)
        if pagamentos_licenciamento_table:
            pagamentos_data = []
            for tr in pagamentos_licenciamento_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 4:
                    exercicio = tds[0].css('::text').get('').strip()
                    guia = tds[1].css('::text').get('').strip()
                    data_transacao = tds[2].css('::text').get('').strip()
                    valor = tds[3].css('::text').get('').strip()
                    if exercicio and guia:
                        pagamentos_data.append({
                            'exercicio': exercicio,
                            'guia': guia,
                            'data_transacao': data_transacao,
                            'valor': valor
                        })
            result['pagamentos_licenciamento'] = pagamentos_data

        # ODÔMETRO - Histórico
        odometro_table = response.css('#content-odometro table.table')
        if odometro_table:
            odometro_data = []
            for tr in odometro_table.css('tr'):
                tds = tr.css('td')
                if len(tds) >= 3:
                    motivo = tr.css('th::text').get('').strip()
                    data = tds[0].css('::text').get('').strip()
                    km = tds[1].css('::text').get('').strip()
                    if motivo and data and km:
                        odometro_data.append({
                            'motivo': motivo,
                            'data': data,
                            'km': km
                        })
            result['odometro'] = odometro_data

        return result



if __name__ == '__main__':
    # from pathlib import Path
    # from execute_spider import process
    #
    # here = Path(__file__).parents[2]
    # file = here / 'definitions'/'autos_detran_pr'/'test'/'-request.json'
    #
    # process('autos_detran_pr_spider', str(file))

    with open('aa_home_search_result_page.html', 'r') as f:
        html = f.read()

    response = Selector(text=html)

    spider = autos_detran_pr_spider
    spider.parse_html(response)
