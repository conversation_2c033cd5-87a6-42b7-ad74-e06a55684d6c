# -*- coding: utf-8 -*-
# origin repository git sha: ff58e9806d4cac57d3a14de8939d3e8568c95e35
import os
import re
import sys
import uuid
from datetime import datetime as dt

import pdftotext
import requests
from bs4 import BeautifulSoup
from scrapy import signals
from scrapy.http import FormRequest, Request

from brobot_bots.external_modules.custom_errors import (<PERSON><PERSON><PERSON>,
                                                        FileNotSaved,
                                                        HtmlError,
                                                        CaptchaNotLoaded,
                                                        InvalidRequestParams,
                                                        UndefinedError,
                                                        KnownWebsiteBug)
from brobot_bots.external_modules.external_functions import CustomSpider, trap_start_requests
from brobot_bots.external_modules import custom_messages
from brobot_bots.external_modules.utils import extract_text
from brobot_bots.external_modules import pdf
from brobot_bots.items import BrobotBotsItem


path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if path not in sys.path:
    sys.path.insert(1, path)
#del path


class autos_prefeitura_manaus_am_spider(CustomSpider):
    # required scraper name
    name = "autos_prefeitura_manaus_am"

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        """Rewriting of the spider_idle function to yield result after spider closed."""

        spider = super(autos_prefeitura_manaus_am_spider, cls).from_crawler(
            crawler, *args, **kwargs)
        crawler.signals.connect(spider.get_final_result, signals.spider_idle)
        return spider

    def __init__(self, *args, **kwargs):
        kwargs.update({
            "max_incorrect_captcha_retries": 5,
            "max_service_retries": 5,
            "exclude_captcha_services": ['BCS', 'DBC']
        })
        super().__init__(*args, **kwargs)
        self.extracted_files = []
        self.validate_empty_keys = True
             
    
    @trap_start_requests
    def start_requests(self):
        if not self.valid_credentials(['renavam']):
            return
        
        if self.review_dates:
            error = InvalidRequestParams(message=custom_messages.INVALID_DATE_FORMAT)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        
        start_url = 'http://grm.prodam.am.gov.br/index.php/multas/consulta_pagamentos'
        yield Request(
            url=start_url,
            callback=self.local_solve_captcha,
            errback=self.errback_func,
            dont_filter=True
        )


    def local_solve_captcha(self, response):
        img_url = response.selector.xpath('//img[@id="captcha"]/@src').get('')
        if not img_url:
            details_msg = "Captcha img wasn't found in response"
            error = CaptchaNotLoaded(message=custom_messages.CAPTCHA_NOT_LOADED, details=details_msg, data=response.text)
            self.logger.error(error)
            self.errors.append(error.to_dict())
            return
        
        img_url = 'http://grm.prodam.am.gov.br' + img_url
        cookies = self.extract_cookies(response=response, _for="session_list")
        captcha_solution_id, captcha_txt = self.get_img_captcha_token(cookies, img_url)

        if captcha_txt is None:
            return
        
        form_data = {
            '_method': 'POST',
            'data[Multa][renavam]': self.renavam,
            'data[Multa][chassi]': '',
            'data[Multa][auto_infracao]': '',
            'data[Multa][cod_seguranca]': captcha_txt
        }

        url = 'http://grm.prodam.am.gov.br/index.php/multas/consulta_pagamentos'
        yield FormRequest(
            url=url,
            formdata=form_data,
            callback=self.parse_result_page,
            errback=self.errback_func,
            cb_kwargs={"captcha_solution_id": captcha_solution_id},
            dont_filter=True
        )


    def parse_result_page(self, response, captcha_solution_id=None):
        try:
            error = response.selector.xpath(".//div[contains(@class, 'alert-error')]/text()").get('')
            if 'NAO HA NOTIFICACOES' in error:
                if self.capture_screenshot:
                    self.upload_screenshot_with_splash_engine(
                        response=response,
                        html=self._insert_credentials_in_html(response)
                    )
                self.result['sem_notificacoes'] = error
                return

            if error == 'Código de seguraça inválido':
                if not self.report_incorrect_captcha(captcha_solution_id):
                    yield from self.start_requests()
                return
            
            if error == 'Error Fetching http headers':
                error = KnownWebsiteBug(message=custom_messages.PAGE_MALFUNCTION, details=error)
                self.logger.error(error)
                self.errors.append(error.to_dict())
                return
            
            if error:
                error = UndefinedError(details=error)
                self.logger.error(error)
                self.errors.append(error.to_dict())
                return

            if self.capture_screenshot:
                self.upload_screenshot_with_splash_engine(
                    response=response
                )

            table_name = response.selector.xpath('(//h4)[2]/text()').get('')
            table_name = self.remove_diacritics(table_name)

            if table_name not in self.result:
                self.result[table_name] = []

            table = response.selector.xpath('//table')
            header_row = [self.remove_diacritics(i) for i in table.xpath('(.//tr)[1]/th/text()').extract()]

            placa = response.selector.xpath("//dt[contains(text(),'Placa')]/following::dd[1]/text()").get("").strip()
            self.result['placa'] = placa
            self.result['renavam'] = self.renavam
            for row in table.xpath('.//tr')[1:]:
                row_data = dict(zip(header_row, [extract_text(cell) for cell in row.xpath('./td')]))
                row_data.pop('guia', None)
                data_e_hora_da_infracao = dt.strptime(row_data['data_e_hora_da_infracao'].split()[0], "%d/%m/%Y")
                if not (self.start_date <= data_e_hora_da_infracao <= self.end_date):
                    continue
                
                if row_data['situacao'] in ['NA', 'NAC']:
                    if not self.get_other_files:
                        self.result[table_name].append(row_data)
                        continue
                    
                if row_data['situacao'] in ['NIC', 'NIP']:
                    if not self.get_boletos:
                        self.result[table_name].append(row_data)
                        continue
                # Check if we found pdf url
                pdf_url = row.xpath(".//a/@href").get('')
                # We are downloading files for every credential
                if pdf_url:
                    pdf_url = 'http://grm.prodam.am.gov.br' + pdf_url
                    file_id = str(uuid.uuid4())
                    file_type = 'boleto'
                    
                    if row_data['situacao'] == 'NA':
                        file_type = 'fici'

                    if row_data['situacao'] == 'NAC':
                        file_type = 'ait'

                    file_type = f'__{file_type}__'

                    meta = {
                        'file_id': file_id,
                        'file_type': file_type,
                        'auto_de_infracao': row_data['auto_de_infracao'],
                        'situacao': row_data['situacao'],
                        "handle_httpstatus_list": [500],
                        'dont_retry': True
                    }                    
                    yield Request(
                        url=pdf_url,
                        callback=self.download_pdf,
                        errback=self.errback_func,
                        dont_filter=True,
                        meta=meta
                    )
                else:
                    details_msg = f'couldn\'t find url for row: {row.extract()}'
                    error = FileNotSaved(message=custom_messages.FILE_NOT_SAVE_PDF, filename="no filename", details=details_msg)
                    self.logger.warning(error)
                    self.errors.append(error.to_dict())

                self.result[table_name].append(row_data)
        except Exception as exc:
            error = HtmlError(error=str(exc), response=response)
            self.logger.warning(error)
            self.errors.append(error.to_dict())


    def _insert_credentials_in_html(self, response):
        """ insert renavam into HTML for screnshot """
        soup = BeautifulSoup(response.text, 'html.parser')

        renavam = soup.select('#MultaRenavam')

        if renavam:
            renavam[0]['value'] = self.renavam

        return str(soup)


    def get_img_captcha_token(self, cookies, img_url):
        ''' This method will only work if you need img based captcha to be solved.
            cookies are list of cookies from response header 'set-cookie' e.g. response.headers.getlist('Set-Cookie')
            img_url is full url to image that needs to be solved
            If captcha is solved it will return captcha text
                that you will use in request '''

        # set cookies to current session
        session = requests.Session()
        for cookie in cookies:
            # print(cookie)
            session.cookies.set(**cookie)

        # save captcha image
        r = session.get(img_url, stream=True)
        with open("captcha.jpg", 'wb') as f:
            f.write(r.content)
        try:
            captcha_solution_id, captcha_txt =\
                self.solve_captcha("NORMAL", captcha_img="captcha.jpg")
            return captcha_solution_id, captcha_txt
        except Error as e:
            return None, None


    def get_final_result(self, spider):
        """Will be called before spider closed
        Used to save data_collected result."""

        # stop crawling after result returned
        if not self.result_received:
            # append extracted data from pdf's:
            [self._append_extracted_file('multas_do_veiculo', *extracted) for extracted in self.extracted_files]
            # You will probably need to change how data_collected_file_name is created
            data_collected_file_name = f'{self.renavam}-data_collected.json'

            if self.screenshots_ids:
                self.result['__screenshots_ids__'] = self.screenshots_ids
            self.data = {
                'scrape_id': self.scrape_id,
                'scraper_name': self.name,
                'files_count': self.files_count,
                'screenshots_count': self.screenshots_count,
                'cnpj': self.cnpj,
                'result': self.result,
                'scraping_scope': self.scraping_scope}
            if self.errors:
                self.data.update({'errors': self.unique_list(self.errors)})

            # push to webhook
            webhook_file_path = os.path.join(path, "downloads", self.scrape_id, data_collected_file_name)
            self.data_collected(self.data, webhook_file_path)
            # return item for scrapinghub
            '''Required to return Scrapy Item'''
            self.result_received = True
            req = Request(
                'https://example.com/',
                callback=self.yield_item,
                errback=self.yield_item,
                dont_filter=True
            )
            self.crawler.engine.crawl(req, spider)


    def yield_item(self, response):
        """Function is using to yield Scrapy Item
        Required for us to see the result in ScrapingHub"""
        item = BrobotBotsItem()
        item.update(self.data)
        yield item


    def download_pdf(self, response):
        ''' Download PDF and save them locally and then call upload_file()'''
   
        file_type = response.meta['file_type']
        auto_de_infracao = response.meta['auto_de_infracao']
        file_id = response.meta['file_id']
        pdf_file_name = f'{file_id}.pdf'
        situacao = response.meta['situacao'] 
        # sometimes response is not PDF, this is just checking so only PDF files are saved
        content_type = response.headers.get('Content-Type', '').decode().lower()
        
        # I encountered this status code during my tests; checking only the content type didn't work
        if response.status  == 500:
            file_unavailable = {'file_unavailable': "Órgão consultado não permitiu download do arquivo neste momento."}
            self.extracted_files.append((auto_de_infracao, file_type, file_unavailable))
            return
        
        if 'pdf' not in content_type:
            self.extracted_files.append((auto_de_infracao, file_type, {'file_unavailable': "Arquivo indisponível no momento."}))
            return

        # save PDF file to folder
        file_path = os.path.join(path, "downloads", self.scrape_id, pdf_file_name)
        with open(file_path, 'wb') as f:
            f.write(response.body)
    
        extracted_file = self._extract_pdf(file_path,file_type,situacao)

        # Check if we are parsing pdf correctly, didn't check 'renainf' yet because it didn't have a standard, and it could be null.
        if file_type == '__boleto__':
            pdf_fields = {
                'vencimento_do_boleto': pdf.validation.FieldType.DATA,
                'linha_digitavel_codigo_barras': pdf.validation.FieldType.CODIGO_BARRAS,
                'valor_cobrado': pdf.validation.FieldType.VALOR,
                'valor_original': pdf.validation.FieldType.VALOR,
            }
            if situacao == 'NIC':
                pdf_fields['ait_origem'] = pdf.validation.FieldType.MIN_2_CHARS

            if not self.is_valid_pdf(extracted_file, pdf_fields):
                self.logger.info(str(file_id))
                return

        if file_type == '__fici__':
            pdf_fields = {
                'data_defesa_previa': pdf.validation.FieldType.DATA,
                'data_indicacao_condutor': pdf.validation.FieldType.DATA,
            }

            if extracted_file.get('data_defesa_previa') == '0,00':
                pdf_fields.pop('data_defesa_previa', '')
                extracted_file['data_defesa_previa'] =  ''

            if not self.is_valid_pdf(extracted_file, pdf_fields):
                self.logger.info(str(file_id))
                return
        
        
        if file_type == '__ait__':
            pdf_fields = {
                'data_defesa_previa': pdf.validation.FieldType.DATA,
                'ait_origem': pdf.validation.FieldType.MIN_2_CHARS,
            }
            if extracted_file.get('data_defesa_previa') == '0,00':
                pdf_fields.pop('data_defesa_previa', '')
                extracted_file['data_defesa_previa'] =  ''

            if not self.is_valid_pdf(extracted_file, pdf_fields):
                self.logger.info(str(file_id))
                return
            

        if self.get_boletos or self.get_other_files:
            self.upload_file(file_id)
            extracted_file['file_id'] = file_id
    
        self.extracted_files.append((auto_de_infracao, file_type, extracted_file))

    
    def _extract_pdf(self, file_path: str, file_type: str, situacao: str):
        """Function using pdftotext to extract pdf's with"""

        # pdftotext is used because when using camelot, camelot can't find EOF in file and it raise error,
        # fix is this PR that is still not merged into PyPDF2: https://github.com/mstamy2/PyPDF2/pull/321
        with open(file_path, 'rb') as f:
            pdf = pdftotext.PDF(f)
        
        if file_type == '__boleto__':
            rows = pdf[0].split('\n')
            extracted = {}
            try:
                for index, row in enumerate(rows[:-1]):
                    row = row.lower()

                    if 'vencimento' in row and re.findall(r'\d{2}/\d{2}/\d{4}', rows[index + 1]):
                        extracted['vencimento_do_boleto'] = rows[index + 1].strip().split()[-1]
                    if 'valor cobrado' in row:
                        extracted['valor_cobrado'] = rows[index + 1].strip().split()[-1]

                    if 'renainf' in row:
                        extracted['renainf'] = rows[index + 1].strip().split()[-2]
                    
                    if re.search(r'\s*valor\s*$',row) or 'valor notifica' in row:
                        extracted['valor_original'] = rows[index + 1].strip().split()[-1]

                    # #valor_original    
                    linha_digitavel = [i.strip() for i in re.findall(r'([\d\. -]{30,})+', row, re.DOTALL) if
                                    len(i.strip().replace(' ', '').replace('-', '')) > 30]
                    if linha_digitavel:
                        extracted['linha_digitavel_codigo_barras'] = linha_digitavel[0]

                    if situacao == 'NIC' and 'agente' in row:
                        values = rows[index+1].split()
                        extracted['ait_origem'] = values[0].strip() if values else ''
            except:
                pass
            
            return extracted

        if file_type == '__fici__':
            extracted = {}
            try:
                dates_search = re.findall(r'(\d+/\d+/\d+)', pdf[0])
                rows = pdf[0].split('\n')
                for index, row in enumerate(rows):
                    row = row.lower() 
                    if 'renainf' in row:
                        renainf_row = rows[index + 1] or rows[index + 2]
                        extracted['renainf'] = renainf_row.strip().split()[-1]
                
                extracted['data_defesa_previa'] = dates_search[-1]
                extracted['data_indicacao_condutor'] = dates_search[-2]
            except:
                pass
            return extracted
            
        if file_type == '__ait__':
            extracted = {}
            try:
                rows = pdf[0].split('\n')
                for index, row in enumerate(rows):
                    row = row.lower() 
                    if 'renainf' in row:
                        values = rows[index+1].split()
                        extracted['renainf'] = values[-2].strip()
                        extracted['data_defesa_previa'] = values[-1].strip()

                    if 'auto de infração' in row:
                        values = rows[index+1].split()
                        extracted['ait_origem'] = values[0].strip() if values else ''
            except:
                pass
            return extracted
        
    def _append_extracted_file(self, result_key, auto_de_infracao, file_type, extracted):
        for multa in self.result[result_key]:
            if multa.get('auto_de_infracao') == auto_de_infracao:
                multa[file_type] = extracted
                break