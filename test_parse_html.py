#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from scrapy import Selector

def parse_html(response):
    result = {}
    
    # RESUMO
    resumo_table = response.css('#content-resumo table.table-striped')
    if resumo_table:
        resumo_data = []
        for tr in resumo_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 2:
                discriminacao = tds[0].css('::text').get('').strip()
                valor = tds[1].css('::text').get('').strip()
                if discriminacao and valor:
                    resumo_data.append({
                        'discriminacao': discriminacao,
                        'valor': valor
                    })
        result['resumo'] = resumo_data
    
    # IPVA
    ipva_table = response.css('#content-ipva table.table-striped')
    if ipva_table:
        ipva_data = []
        for tr in ipva_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 2:
                discriminacao = tds[0].css('::text').get('').strip()
                valor = tds[1].css('::text').get('').strip()
                if discriminacao and valor and 'Total' not in discriminacao:
                    ipva_data.append({
                        'discriminacao': discriminacao,
                        'valor': valor
                    })
        result['ipva'] = ipva_data
    
    # LICENCIAMENTO
    licenciamento_table = response.css('#content-licenciamento table.table-striped')
    if licenciamento_table:
        licenciamento_data = []
        for tr in licenciamento_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 3:
                discriminacao = tds[0].css('::text').get('').strip()
                vencimento = tds[1].css('::text').get('').strip()
                valor = tds[2].css('::text').get('').strip()
                if discriminacao and valor and 'TOTAL' not in discriminacao:
                    licenciamento_data.append({
                        'discriminacao': discriminacao,
                        'vencimento': vencimento,
                        'valor': valor
                    })
        result['licenciamento'] = licenciamento_data
    
    # MULTAS - Resumo das Multas de Trânsito
    multas_resumo_table = response.css('#content-multas_multas table.table-striped').first()
    if multas_resumo_table:
        multas_resumo_data = []
        for tr in multas_resumo_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 3:
                discriminacao = tds[0].css('::text').get('').strip()
                quantidade = tds[1].css('::text').get('').strip()
                valor = tds[2].css('::text').get('').strip()
                if discriminacao and 'TOTAL' not in discriminacao:
                    multas_resumo_data.append({
                        'discriminacao': discriminacao,
                        'quantidade': quantidade,
                        'valor': valor
                    })
        result['multas_resumo'] = multas_resumo_data
    
    # MULTAS - Resumo das Autuações de Trânsito
    autuacoes_table = response.css('#content-multas_multas table.table-striped').eq(1)
    if autuacoes_table:
        autuacoes_data = []
        for tr in autuacoes_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 3:
                discriminacao = tds[0].css('::text').get('').strip()
                quantidade = tds[1].css('::text').get('').strip()
                valor = tds[2].css('::text').get('').strip()
                if discriminacao and 'TOTAL' not in discriminacao:
                    autuacoes_data.append({
                        'discriminacao': discriminacao,
                        'quantidade': quantidade,
                        'valor': valor
                    })
        result['autuacoes'] = autuacoes_data
    
    # MULTAS PAGAS
    multas_pagas_table = response.css('#content-multas_pagas table#table-multas-pagas')
    if multas_pagas_table:
        multas_pagas_data = []
        for tr in multas_pagas_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 4:
                data = tds[0].css('::text').get('').strip()
                infracao = tds[1].css('::text').get('').strip()
                data_pagamento = tds[2].css('::text').get('').strip()
                valor_pago = tds[3].css('::text').get('').strip()
                if data and infracao:
                    multas_pagas_data.append({
                        'data': data,
                        'infracao': infracao,
                        'data_pagamento': data_pagamento,
                        'valor_pago': valor_pago
                    })
        result['multas_pagas'] = multas_pagas_data
    
    # EMISSÃO LICENCIAMENTO - Débitos para emissão do CRLV
    crlv_debitos_table = response.css('#content-emissao-licenciamento table.table-striped').first()
    if crlv_debitos_table:
        crlv_debitos_data = []
        for tr in crlv_debitos_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 2:
                discriminacao = tds[0].css('::text').get('').strip()
                valor = tds[1].css('::text').get('').strip()
                if discriminacao and valor:
                    crlv_debitos_data.append({
                        'discriminacao': discriminacao,
                        'valor': valor
                    })
        result['crlv_debitos'] = crlv_debitos_data
    
    # EMISSÃO LICENCIAMENTO - Últimos Pagamentos do Licenciamento
    pagamentos_licenciamento_table = response.css('#content-emissao-licenciamento table.table-striped').eq(1)
    if pagamentos_licenciamento_table:
        pagamentos_data = []
        for tr in pagamentos_licenciamento_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 4:
                exercicio = tds[0].css('::text').get('').strip()
                guia = tds[1].css('::text').get('').strip()
                data_transacao = tds[2].css('::text').get('').strip()
                valor = tds[3].css('::text').get('').strip()
                if exercicio and guia:
                    pagamentos_data.append({
                        'exercicio': exercicio,
                        'guia': guia,
                        'data_transacao': data_transacao,
                        'valor': valor
                    })
        result['pagamentos_licenciamento'] = pagamentos_data
    
    # ODÔMETRO - Histórico
    odometro_table = response.css('#content-odometro table.table')
    if odometro_table:
        odometro_data = []
        for tr in odometro_table.css('tr'):
            tds = tr.css('td')
            if len(tds) >= 3:
                motivo = tr.css('th::text').get('').strip()
                data = tds[0].css('::text').get('').strip()
                km = tds[1].css('::text').get('').strip()
                if motivo and data and km:
                    odometro_data.append({
                        'motivo': motivo,
                        'data': data,
                        'km': km
                    })
        result['odometro'] = odometro_data
    
    return result

if __name__ == '__main__':
    import json
    
    with open('brobot_bots/spiders/aa_home_search_result_page.html', 'r', encoding='utf-8') as f:
        html = f.read()

    response = Selector(text=html)
    result = parse_html(response)
    
    print(json.dumps(result, indent=2, ensure_ascii=False))
